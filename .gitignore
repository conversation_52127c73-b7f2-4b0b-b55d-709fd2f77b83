# Python bytecode & cache
__pycache__/
*.py[cod]
*$py.class
.vscode/
.idea/
# Data directories - ignore large datasets but keep structure
yoloserver/data/*
!yoloserver/data/.keep
!yoloserver/data/*/.keep
!yoloserver/data/*/*/.keep
!yoloserver/data/crawled

# Logs directories
yoloserver/logs/*
!yoloserver/logs/.keep
!yoloserver/logs/*/.keep

# Configs directories
yoloserver/configs/*
!yoloserver/configs/.keep

yoloserver/runs/*

# Django static files (auto-generated)
django_frontend/staticfiles/
django_frontend/media/

# Database
django_frontend/db.sqlite3

# Environment variables
.env
.env.local
