# **医学图像检测系统总体需求文档**

## **文档信息**

- **文档编号**：REQ-YOLO-MEDICAL-001
- **版本**：2.0
- **作者**：雨霓
- **创建日期**：2025年6月14日
- **更新日期**：2025年6月14日
- **状态**：草稿
- **审核人**：技术负责人（待定）
- **分发范围**：数据团队、开发团队、测试团队、项目经理、前端团队

## **1. 项目背景**

本项目旨在开发一个基于 Ultralytics YOLOv8/YOLOv11 的医学图像分割与检测系统，用于检测医学影像（如 MRI 或 CT 图像）中的肿瘤和占位性病变，类别包括 、`glioma`（胶质瘤）、`meningioma`（脑膜瘤）、`pituitary`（垂体瘤）和 `space-occupying lesion`（占位性病变）。系统涵盖数据集转换、模型训练、模型验证、模型推理、结果美化以及基于 Django 的 Web 前后端交互五个核心模块，并通过调用 LLM 模型 API 生成检测建议和 PDF 报告，目标是提供自动化、标准化、高效的工作流程，减少手动操作，提升检测准确性和结果展示的直观性，为医学影像分析提供技术支持。

## **2. 目标**

### **2.1 业务目标**
- 实现从原始 COCO JSON 数据到模型推理的端到端自动化流程，减少 50% 的手动操作时间。
- 确保数据集准备、训练和验证结果符合 YOLOv8/YOLOv11 标准，支持高质量模型开发。
- 提供基于 Django 的 Web 平台，支持用户上传图像/视频、查看推理结果、获取 LLM 生成的建议，并下载 PDF 检测报告。
- 确保系统稳定性和可追溯性，满足医学影像分析的实验分析和审计需求。

### **2.2 技术目标**
- 开发数据集转换模块，将 COCO JSON 格式转换为 YOLO 格式（检测为 `class_id center_x center_y width height`，分割为 `class_id x1 y1 x2 y2 ...`），生成标准目录结构和 `data.yaml`。
- 实现模型训练和验证脚本，支持命令行和 YAML 参数配置，统一日志和结果保存。
- 提供灵活的推理脚本，支持图像、视频、文件夹输入，集成 Web 界面。
- 确保跨平台兼容性（Windows/Linux）、健壮的错误处理和统一的路径管理。
- 开发 Django Web 应用，支持文件上传、结果可视化、LLM 建议生成和 PDF 报告导出。
- 集成统一的日志系统，记录所有关键操作、设备信息和耗时统计。

### **2.3 交付目标**
- 交付脚本：`initialize_project.py`, `scripts/yolo_trans.py`, `scripts/yolo_train.py`, `scripts/yolo_val.py`, `scripts/yolo_infer.py`。
- 交付 Django Web 应用，包含前端页面和后端 API。
- 交付 `utils` 工具包，包含路径、日志、数据处理、验证、可视化和 PDF 生成模块。
- 提供 README 和测试报告，说明各模块用法和执行顺序。
- 完成 Git 提交，分支为 `feature/medical-pipeline`。

## **3. 假设与约束**
- **假设**：
  - 原始数据为 COCO JSON 格式，包含 `images`, `annotations`, `categories` 字段，标注包括边界框和分割掩码（多边形坐标）。
  - 图像文件格式为 `.jpg`, `.jpeg`, `.png`, `.bmp`, `.tiff`, `.webp`，文件名与 COCO JSON 标注匹配。
  - 运行环境为 Python 3.12+，安装了必要依赖库（包括 Django 和 LaTeX 环境）。
  - 类别列表为：`['NO_tumor', 'glioma', 'meningioma', 'pituitary', 'space-occupying lesion']`。
  - LLM 模型 API 可通过 HTTPS 调用，返回 JSON 格式建议。
- **约束**：
  - 不支持其他标注格式（如 Pascal VOC XML），但预留扩展接口。
  - 数据集分割比例总和必须为 1。
  - 推理模块需支持图像和视频输入，Web 平台需支持文件上传、结果下载和 PDF 报告生成。
  - Web 应用需保证上传文件大小限制（默认 < 100MB）。
  - PDF 报告需使用 LaTeX 生成，基于 PDFLaTeX 引擎。

## **4. 功能需求**

### **4.1 项目初始化 (`initialize_project.py`)**
- **FR-001：目录结构创建**（优先级：高）
  - 在 `MedicalYOLO/` 创建标准目录结构：
    ```
    MedicalYOLO/
    ├── configs/                                # 配置文件
    ├── data/                                   # 数据集
    │   ├── raw/                                # 原始数据
    │   │   ├── images/                         # 原始图像
    │   │   └── annotations/                    # 原始 COCO JSON 标注
    │   ├── train/                              # 训练集
    │   │   ├── images/
    │   │   └── labels/
    │   ├── val/                                # 验证集
    │   │   ├── images/
    │   │   └── labels/
    │   └── test/                               # 测试集
    │       ├── images/
    │       └── labels/
    ├── logging/                                # 日志
    │   ├── project_init/                       # 初始化日志
    │   ├── data_conversion/                    # 转换日志
    │   ├── train/                              # 训练日志
    │   ├── val/                                # 验证日志
    │   └── infer/                              # 推理日志
    ├── runs/                                   # 训练/验证/推理结果
    │   ├── detect/                             # 训练结果
    │   ├── val/                                # 验证结果
    │   └── infer/                              # 推理结果
    ├── models/                                 # 模型文件
    │   ├── pretrained/                         # 预训练模型
    │   └── checkpoints/                        # 训练后模型
    ```
  - 检查目录是否存在，若存在则跳过创建，不覆盖文件。
- **FR-002：原始数据引导**（优先级：中）
  - 提示用户将图像和 COCO JSON 文件放入 `data/raw/images/` 和 `data/raw/annotations/`。
  - 检查 `data/raw/` 状态，记录文件数量。
- **FR-003：日志记录**（优先级：高）
  - 使用 `utils.logging_utils.setup_logging(base_path, log_type='project_init', temp_log=True)`。
  - 日志文件名为 `temp-时间戳-project-init.log`，保存到 `logging/project_init/`。
  - 重命名为 `initN-时间戳-project-init.log`。

### **4.2 数据集转换与分割 (`scripts/yolo_trans.py`)**
- **FR-004：数据读取与匹配**（优先级：高）
  - 读取 `data/raw/images/` 和 `data/raw/annotations/`，匹配图像和 COCO JSON 文件。
  - 记录未匹配文件。
- **FR-005：格式转换**（优先级：高）
  - 解析 COCO JSON，提取 `images`, `annotations`（边界框和分割掩码）信息。
  - 转换为 YOLO 格式：
    - 检测：`class_id center_x center_y width height`，归一化到 [0, 1]，精度 6 位。
    - 分割：`class_id x1 y1 x2 y2 ...`，归一化到 [0, 1]，精度 6 位，基于多边形坐标。
  - 保存为 `.txt` 文件，空标注生成空文件。
- **FR-006：数据集分割**（优先级：高）
  - 支持命令行参数或 `configs/data_split.yaml` 配置分割比例（默认 0.8:0.1:0.1）。
  - 使用固定随机种子。
  - 复制文件到 `data/train/`, `data/val/`, `data/test/`。
- **FR-007：`data.yaml` 生成**（优先级：高）
  - 生成 `configs/data.yaml`，包含绝对路径、类别数（`nc=5`）和类别列表。
- **FR-008：日志记录**（优先级：高）
  - 日志文件名为 `temp-时间戳-data-conversion.log`，保存到 `logging/data_conversion/`。
  - 记录匹配、转换、分割、`data.yaml` 生成信息。

### **4.3 模型训练 (`scripts/yolo_train.py`)**
- **FR-009：模型与配置文件加载**（优先级：高）
  - 从 `models/pretrained/` 加载用户指定的预训练模型（如 `yolov8n.pt` 或 `yolov8n-seg.pt`），若不存在则自动下载。
  - 从 `configs/` 加载 `data.yaml` 或用户指定的 YAML 文件。
  - 支持命令行参数（如 `--model`, `--data`, `--epochs`, `--batch`, `--imgsz`, `--lr0`, `--device`）和 `configs/train.yaml` 配置，优先级：CLI > YAML > 默认值。
- **FR-010：训练执行**（优先级：高）
  - 使用 YOLOv8 API 执行检测和分割任务训练，保存权重到 `runs/detect/trainN/` 或 `runs/segment/trainN/`。
  - 复制 `best.pt` 和 `last.pt` 到 `models/checkpoints/`，命名如 `trainN-时间戳-yolov8n-best.pt`。
- **FR-011：日志记录**（优先级：高）
  - 日志文件名为 `temp-时间戳-train.log`，保存到 `logging/train/`，编码为 `utf-8-sig`。
  - 记录设备（OS, GPU/CPU, 显存）、数据集（类别数、名称、样本数）、训练参数、mAP@50、mAP@50:95、耗时。
  - 重命名为 `trainN-时间戳-yolov8n.log`。
  - 示例：
    ```
    2025-06-14 20:00:01,123 - YOLO_Train - INFO - 训练开始
    2025-06-14 20:00:01,124 - YOLO_Train - INFO - 设备: OS=Windows-11, GPU=RTX 3070 (8GB)
    2025-06-14 20:00:01,125 - YOLO_Train - INFO - 数据集: 类别数=5, 名称=[NO_tumor, glioma, ...], 训练样本=800
    2025-06-14 20:00:01,126 - YOLO_Train - INFO - 参数: epochs=50, batch=16, imgsz=640, lr0=0.01
    2025-06-14 20:30:01,127 - YOLO_Train - INFO - 训练结果: mAP@50=0.870, mAP@50:95=0.650
    2025-06-14 20:30:01,128 - YOLO_Train - INFO - 日志重命名: train1-20250614_200001-yolov8n.log
    ```

### **4.4 模型验证 (`scripts/yolo_val.py`)**
- **FR-012：模型与配置文件加载**（优先级：高）
  - 从 `models/checkpoints/` 加载用户指定权重（如 `trainN-时间戳-yolov8n-best.pt`）。
  - 从 `configs/` 加载 `data.yaml`。
  - 支持命令行参数（如 `--weights`, `--data`, `--imgsz`, `--device`）和 `configs/val.yaml`，优先级：CLI > YAML > 默认值。
- **FR-013：验证执行**（优先级：高）
  - 使用 YOLOv8 API 执行检测和分割任务验证，保存结果到 `runs/val/validationN/` 或 `runs/segment_val/validationN/`。
  - 输出 mAP@50、mAP@50:95、精确率、召回率到控制台。
- **FR-014：日志记录**（优先级：高）
  - 日志文件名为 `temp-时间戳-val.log`，保存到 `logging/val/`，编码为 `utf-8-sig`。
  - 记录设备、数据集、模型、验证结果、耗时。
  - 重命名为 `valN-时间戳-yolov8n.log`。
- **FR-015：耗时可视化**（优先级：中）
  - 生成 `time_stats.png`，保存到 `runs/val/validationN/` 或 `runs/segment_val/validationN/`。

### **4.5 模型推理 (`scripts/yolo_infer.py`)**
- **FR-016：输入支持**（优先级：高）
  - 支持图像、文件夹、视频输入，集成到 Django Web 应用。
  - 提供退出机制（如 Web 界面停止推理按钮）。
  - 支持命令行参数（如 `--source`, `--weights`, `--conf`, `--imgsz`）和 `configs/infer.yaml`。
- **FR-017：推理执行**（优先级：高）
  - 加载 `models/checkpoints/` 中的权重，执行检测和分割推理。
  - 保存结果到 `runs/infer/inferN/`（图像/视频带检测框和分割掩码）。
- **FR-018：结果美化**（优先级：高）
  - 生成带圆角标签的检测框和分割掩码可视化，支持中文标签显示。
  - 动态调整标签大小和位置，随图像分辨率自适应。
- **FR-019：日志记录**（优先级：高）
  - 日志文件名为 `temp-时间戳-infer.log`，保存到 `logging/infer/`。
  - 记录输入源、设备、推理耗时、检测结果。

### **4.6 Django Web 应用 (`web/medical_app/`)**
- **FR-020：文件上传**（优先级：高）
  - 支持用户通过 Web 界面上传图像或视频（< 100MB）。
  - 保存上传文件到 `data/raw/images/`。
- **FR-021：推理与可视化**（优先级：高）
  - 调用 `yolo_infer.py` 执行推理，展示检测框和分割掩码结果。
  - 支持结果下载（图像/视频）。
- **FR-022：用户界面**（优先级：高）
  - 使用 Tailwind CSS 设计响应式界面。
  - 提供上传、推理、结果查看、LLM 建议查看和 PDF 报告下载功能。
- **FR-023：LLM 建议生成**（优先级：高）
  - 根据推理结果（如检测到的 `glioma`, `meningioma` 等），调用 LLM 模型 API（通过 HTTPS POST 请求，输入检测结果 JSON，输出建议文本）。
  - 展示 LLM 生成的建议（如“建议进一步进行 MRI 检查”）在 Web 界面。
- **FR-024：PDF 报告生成**（优先级：高）
  - 使用 LaTeX（PDFLaTeX 引擎）生成包含检测结果（图像、检测框、分割掩码）、置信度、类别和 LLM 建议的 PDF 报告。
  - 保存报告到 `runs/infer/inferN/reports/`，命名如 `reportN-时间戳.pdf`。
  - 支持用户通过 Web 界面下载 PDF 报告。
- **FR-025：日志记录**（优先级：高）
  - 记录 Web 应用操作日志（上传、推理、LLM 调用、PDF 生成），保存到 `logging/web/`，文件名为 `temp-时间戳-web.log`，重命名为 `webN-时间戳.log`。

### **4.7 通用功能模块 (`utils/` 包)**
- **FR-026：路径管理**（优先级：高）
  - `utils/paths.py` 定义绝对路径常量。
- **FR-027：日志管理**（优先级：高）
  - `utils/logging_utils.py` 提供 `setup_logging()` 和 `rename_log_file()`，支持 `utf-8-sig`。
- **FR-028：数据处理**（优先级：高）
  - `utils/data_processing.py` 封装 COCO JSON 解析和 YOLO 格式转换。
- **FR-029：验证核心**（优先级：高）
  - `utils/dataset_validation.py` 验证 `data.yaml` 和标签内容。
- **FR-030：可视化核心**（优先级：中）
  - `utils/visualization.py` 封装圆角标签和分割掩码可视化。
- **FR-031：PDF 生成**（优先级：高）
  - `utils/pdf_generator.py` 封装 LaTeX 模板，生成包含检测结果和 LLM 建议的 PDF 报告。

### **4.8 错误处理与健壮性**
- **FR-032：文件/目录错误**（优先级：高）
  - 捕获 `IOError`, `OSError`, `FileNotFoundError`，记录错误。
- **FR-033：数据解析错误**（优先级：高）
  - 捕获 `json.JSONDecodeError`, `ValueError`，跳过问题文件。
- **FR-034：参数验证**（优先级：高）
  - 验证分割比例、训练/推理参数有效性。
- **FR-035：Web 错误**（优先级：高）
  - 捕获上传文件过大、格式错误、LLM API 调用失败等异常，返回友好提示。
- **FR-036：PDF 生成错误**（优先级：高）
  - 捕获 LaTeX 编译错误，记录详细日志并提示用户。

## **5. 非功能需求**
- **NF-001：代码规范**：遵循 PEP 8，包含文档字符串，变量用 snake_case。
- **NF-002：兼容性**：支持 Windows/Linux，Python 3.12+，Django 5.0+，PDFLaTeX。
- **NF-003：性能**：1000 张图像的转换/验证 < 5 分钟，推理每帧 < 0.1 秒（GPU），PDF 生成 < 5 秒/份。
- **NF-004：可维护性**：模块化设计，清晰日志。
- **NF-005：可靠性**：容错处理异常数据，LLM API 调用失败时提供默认提示。
- **NF-006：可扩展性**：支持未来扩展（如其他标注格式）。
- **NF-007：文档**：README 包含用法、参数、依赖；测试报告记录结果。
- **NF-008：安全性**：Web 应用限制文件上传大小，防止恶意文件，LLM API 调用使用安全认证。

## **6. 技术要求**
- **编程语言**：Python 3.12+。
- **核心库**：`ultralytics`, `pyyaml`, `opencv-python`, `numpy`, `matplotlib`, `django`, `djangorestframework`, `requests`（LLM API 调用），`reportlab` 或 `pylatex`（PDF 生成），标准库（`pathlib`, `logging`, `shutil`）。
- **前端技术**：HTML, Tailwind CSS, JavaScript。
- **LaTeX 引擎**：PDFLaTeX，使用 `geometry`, `graphicx`, `fontenc`, `inputenc`, `ctex`（支持中文）等包。
- **项目结构**：
  ```
  MedicalYOLO/
  └── yoloserver/
      ├── initialize_project.py
      ├── scripts/
      │   ├── yolo_trans.py
      │   ├── yolo_train.py
      │   ├── yolo_val.py
      │   └── yolo_infer.py
      ├── utils/
      │   ├── paths.py
      │   ├── logging_utils.py
      │   ├── data_processing.py
      │   ├── dataset_validation.py
      │   ├── visualization.py
      │   └── pdf_generator.py
      ├── configs/
      │   ├── data.yaml
      │   ├── train.yaml
      │   ├── val.yaml
      │   └── infer.yaml
      ├── data/
      ├── logging/
      ├── runs/
      ├── models/
      ├── web/
      │   ├── static/
      │   ├── templates/
      │   └── medical_app/
      ├── requirements.txt
      └── README.md
  ```
- **版本控制**：Git，分支 `feature/medical-pipeline`.

## **7. 验收标准**
- **AC-001：项目初始化**：正确创建目录结构，日志记录操作。
- **AC-002：数据集转换**：生成 YOLO 格式标签（检测和分割）、`data.yaml`，分割正确。
- **AC-003：模型训练**：生成 `best.pt`, `last.pt`，日志记录参数和结果。
- **AC-004：模型验证**：输出核心指标，保存结果到 `runs/val/validationN/` 或 `runs/segment_val/validationN/`。
- **AC-005：模型推理**：支持多种输入，生成美化结果。
- **AC-006：Web 应用**：支持文件上传、推理、结果展示、LLM 建议查看和 PDF 报告下载。
- **AC-007：LLM 建议**：正确调用 LLM API，展示建议。
- **AC-008：PDF 报告**：生成包含检测结果和建议的 PDF，格式清晰，支持中文。
- **AC-009：日志质量**：日志文件按约定命名，包含设备、耗时、错误信息。
- **AC-010：代码质量**：符合 PEP 8，无 linting 警告。
- **AC-011：性能**：转换/验证 < 5 分钟，推理 < 0.1 秒/帧，PDF 生成 < 5 秒/份。
- **AC-012：文档**：README 和测试报告完整。

## **8. 开发流程**
- **流程**：
  1. 需求分析，确认模块接口。
  2. 开发 `utils/` 模块（包括 PDF 生成）。
  3. 开发初始化、转换、训练、验证、推理脚本。
  4. 开发 Django Web 应用（前后端，集成 LLM API 和 PDF 生成）。
  5. 集成测试。
  6. 编写 README 和测试报告。
  7. 提交 Pull Request。
- **时间**：2025年6月15日 - 6月25日（9 个工作日）。
- **职责**：
  - 开发：代码实现、单元测试。
  - 测试：功能、性能、兼容性测试。
  - 经理：需求评审、进度跟踪。

## **9. 风险与缓解措施**
- **R-001：数据格式不规范**：增加校验，记录错误并跳过。
- **R-002：性能瓶颈**：优化 I/O，使用多线程。
- **R-003：路径兼容性**：使用 `pathlib.Path`。
- **R-004：LLM API 不可用**：提供默认建议，缓存上次结果。
- **R-005：Web 安全**：限制上传文件大小，验证文件格式。
- **R-006：LaTeX 编译错误**：预定义模板，记录详细错误日志。

## **10. 交付物**
- **代码**：`initialize_project.py`, `scripts/*.py`, `utils/` 包，`web/` 目录。
- **配置**：`configs/data.yaml`, `train.yaml`, `val.yaml`, `infer.yaml`。
- **文档**：README、测试报告。
- **Git 提交**：分支 `feature/medical-pipeline`。

## **11. 参考资源**
- **内部代码**：`utils/paths.py`, `utils/logging_utils.py`, `utils/data_processing.py`, `utils/dataset_validation.py`, `utils/visualization.py`, `utils/pdf_generator.py`。
- **外部文档**：
  - Ultralytics YOLOv8/YOLOv11（https://docs.ultralytics.com/）。
  - COCO 数据集规范（http://cocodataset.org/）。
  - Django 文档（https://docs.djangoproject.com/）。
  - Python 文档（https://docs.python.org/3/）。
  - LaTeX 文档（https://www.latex-project.org/）。

## **12. 审批流程**
- 提交 `feature/medical-pipeline` 分支。
- 两名开发者审查代码。
- 测试团队验证功能。
- 项目经理审核文档。
- 合并至 `main`，归档文档。