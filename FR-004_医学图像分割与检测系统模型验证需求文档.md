# YOLOv8 模型验证脚本开发需求（进阶版）

## 文档信息
- **文档编号**：REQ-YOLO-TUMOR-VALIDATE-001
- **版本**：1.0
- **作者**：雨霓
- **创建日期**：2025年6月15日
- **更新日期**：2025年6月15日 12:27 AM AEST
- **状态**：草稿
- **审核人**：待定
- **分发范围**：开发团队、测试团队、项目经理

## 1. 项目背景
本项目为医学影像分割检测系统的一部分，基于 Ultralytics YOLOv8/YOLOv11，开发进阶版模型验证脚本（`yolo_validate.py`），用于肿瘤检测（包括 glioma、meningioma、pituitary 等）。目标是提升验证过程的可复现性、健壮性和实用性。现有训练脚本（REQ-YOLO-TUMOR-TRAIN-002）已实现日志功能、参数管理、设备/数据集信息记录等。本需求聚焦模型验证脚本开发，确保验证结果可追溯、审计和调试便捷，支持实验复现。

## 2. 目标
- **业务目标**：
  - 提供详细验证记录（参数来源、设备、数据集、结果），支持实验可复现性和审计。
  - 生成标准化日志和结果目录，简化分析和报告生成。
- **技术目标**：
  - 复用训练脚本的日志系统和参数管理模块，记录验证参数、设备和数据集信息。
  - 实现模型验证逻辑，记录指标（如 mAP@50、mAP@50:95）和耗时。
  - 模块化设计，支持未来扩展（如多数据集验证）。
- **交付目标**：
  - 交付 `yolo_validate.py` 和相关工具模块，优先完成验证逻辑和结果记录。
  - 更新 README 和测试报告，符合企业规范。

## 3. 功能模块
### 3.1 功能模块概览
以下为功能模块清单，按优先级排序（高：必须实现；中：时间允许实现；低：可选）：

| 功能编号 | 模块名称                   | 优先级 | 状态     | 开发理由                                                                 | 依赖模块                     |
|----------|---------------------------|--------|----------|-------------------------------------------------------------------------|-----------------------------|
| FR-000   | 日志功能（初始化+内容）    | 高     | 已实现   | 核心记录工具，确保验证过程可追溯，满足审计和调试需求                     | 无                          |
| FR-001   | 日志重命名（rename_log_file） | 高     | 已实现   | 标准化日志命名，便于管理多轮验证                                         | FR-000                     |
| FR-002   | 加载 YAML 配置（load_yaml_config） | 高     | 已实现   | 加载配置文件，确保参数可追溯，减少配置错误                               | 无                          |
| FR-003   | 生成默认 YAML（generate_default_yaml） | 高     | 已实现   | 配置文件缺失时生成默认值，提升脚本健壮性和用户体验                       | FR-002                     |
| FR-004   | 参数合并（merge_configs）  | 高     | 已实现   | 合并 CLI、YAML、默认参数，确保灵活性和一致性                             | FR-002, FR-003             |
| FR-005   | 设备信息（system_utils.py） | 中     | 已实现   | 记录硬件环境（如 GPU、PyTorch 版本），支持实验复现和调试                | FR-000                     |
| FR-006   | 数据集信息（dataset_utils.py） | 中     | 已实现   | 记录数据集信息（如类别数、样本数），确保数据一致性和审计                | FR-000                     |
| FR-007   | 组合信息（log_validation_info） | 中     | 未实现   | 整合设备、数据集、参数信息到日志，提升可复现性                         | FR-000, FR-005, FR-006     |
| FR-008   | 模型拷贝（copy_checkpoint_models） | 中     | 已实现   | 拷贝验证模型到结果目录，便于版本管理和复现                              | 无                          |
| FR-009   | 主验证脚本（日志内容、错误处理、控制台输出） | 高     | 未实现   | 整合验证逻辑、错误处理和结果反馈，提升健壮性和用户体验                   | FR-000, FR-002-008         |

### 3.2 功能模块详情
#### FR-000：日志功能（初始化+内容）（优先级：高，已实现）
- **开发理由**：
  - 日志是验证过程的核心记录工具，保存参数、设备、结果等信息，确保实验可复现性和审计性。
  - 标准化日志初始化（如 `temp-YYYYMMDD-HHMMSS-yolov8n.log`）为其他模块（如 FR-001、FR-007）提供基础。
- **实现思路**（已实现）：
  - 调用 `setup_logging(base_path, log_type='validate', model_name='yolov8n', encoding='utf-8-sig')`。
  - 使用 `logging` 模块，配置 INFO 级别，格式：`时间 - 模块 - 消息`。
  - 保存日志到 `base_path/logging/validate`，文件名基于时间戳和模型名（如 `temp-20250615-002700-yolov8n.log`）。
  - 示例：
    ```python
    import logging
    from pathlib import Path
    from datetime import datetime
    def setup_logging(base_path, log_type='validate', model_name='yolov8n', encoding='utf-8-sig'):
        log_dir = Path(base_path) / 'logging' / log_type
        log_dir.mkdir(parents=True, exist_ok=True)
        logging.basicConfig(
            filename=log_dir / f'temp-{datetime.now().strftime("%Y%m%d-%H%M%S")}-{model_name}.log',
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            encoding=encoding
        )
    ```

#### FR-001：日志重命名（rename_log_file）（优先级：高，已实现）
- **开发理由**：
  - 标准化日志命名（如 `validateN-YYYYMMDD_HHMMSS-yolov8n.log`）便于管理多轮验证，避免文件冲突。
  - 递增编号（`validateN`）支持实验区分，满足企业级日志管理规范。
- **实现思路**（已实现）：
  - 调用 `rename_log_file`，扫描 `logs/validate` 目录，获取最大编号 `N`。
  - 重命名临时日志（如 `temp-20250615-002700-yolov8n.log` → `validate1-20250615_002700-yolov8n.log`）。
  - 示例：
    ```python
    from pathlib import Path
    from datetime import datetime
    def rename_log_file(temp_log_path: Path, model_name: str) -> Path:
        log_dir = temp_log_path.parent
        existing_logs = [f.name for f in log_dir.glob('validate*.log')]
        n = max([int(f.split('-')[0].replace('validate', '')) for f in existing_logs if f.startswith('validate')] or [0]) + 1
        new_name = log_dir / f'validate{n}-{datetime.now().strftime("%Y%m%d_%H%M%S")}-{model_name}.log'
        temp_log_path.rename(new_name)
        logging.info(f'日志重命名: {new_name}')
        return new_name
    ```

#### FR-002：加载 YAML 配置（load_yaml_config）（优先级：高，已实现）
- **开发理由**：
  - 加载 `data.yaml` 配置文件提供验证参数（如 `val`, `nc`, `names`），是参数管理的核心。
  - 确保参数可追溯，减少配置错误，支持实验复现。
- **实现思路**（已实现）：
  - 使用 `pyyaml` 加载 `data.yaml`，解析字段（如 `val`, `nc`, `names`）。
  - 检查文件存在性和格式，抛出异常（如 `FileNotFoundError`）并记录日志。
  - 返回配置字典，供后续合并（FR-004）使用。
  - 示例：
    ```python
    import yaml
    from pathlib import Path
    def load_yaml_config(yaml_path: str) -> dict:
        try:
            with open(yaml_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f) or {}
            logging.info(f'加载配置文件: {yaml_path}')
            return config
        except FileNotFoundError:
            logging.error(f'配置文件不存在: {yaml_path}')
            raise
        except yaml.YAMLError as e:
            logging.error(f'YAML 解析错误: {e}')
            raise
    ```

#### FR-003：生成默认 YAML（generate_default_yaml）（优先级：高，已实现）
- **开发理由**：
  - 配置文件缺失时生成默认 YAML，提升脚本健壮性和用户体验。
  - 提供默认参数（如 `val: images/val`, `nc: 6`），确保验证可继续。
- **实现思路**（已 реализован）：
  - 检查 `data.yaml` 是否存在，若不存在则生成默认配置文件。
  - 默认参数：`val: images/val`, `nc: 6`, `names: ['glioma-meningioma-pituitary-No', 'NO_tumor', 'glioma', 'meningioma', 'pituitary', 'space-occupying lesion']`, `batch: 16`.
  - 保存到指定路径（如 `configs/data.yaml`），记录日志。
  - 示例：
    ```python
    import yaml
    from pathlib import Path
    def generate_default_yaml(yaml_path: str) -> dict:
        default_config = {
            'val': 'images/val',
            'nc': 6,
            'names': ['glioma-meningioma-pituitary-No', 'NO_tumor', 'glioma', 'meningioma', 'pituitary', 'space-occupying lesion'],
            'batch': 16
        }
        yaml_path = Path(yaml_path)
        yaml_path.parent.mkdir(parents=True, exist_ok=True)
        with open(yaml_path, 'w', encoding='utf-8') as f:
            yaml.safe_dump(default_config, f)
        logging.info(f'生成默认配置文件: {yaml_path}')
        return default_config
    ```

#### FR-004：参数合并（merge_configs）（优先级：高，已实现）
- **开发理由**：
  - 合并命令行（CLI）、YAML 和默认参数（优先级：CLI > YAML > 默认值），确保配置灵活性和一致性。
  - 避免参数冲突，提升用户体验，满足可复现性要求。
- **实现思路**（已实现）：
  - 使用 `argparse` 解析 CLI 参数（如 `--batch`, `--data`）。
  - 调用 `load_yaml_config`（FR-002）获取 YAML 参数，若失败则调用 `generate_default_yaml`（FR-003）。
  - 合并参数：CLI 参数覆盖 YAML，YAML 覆盖默认值。
  - 记录合并后的参数及来源到日志（依赖 FR-000）。
  - 示例：
    ```python
    import argparse
    from pathlib import Path
    def merge_configs() -> dict:
        parser = argparse.ArgumentParser()
        parser.add_argument('--batch', type=int)
        parser.add_argument('--data', type=str, default='configs/data.yaml')
        args = parser.parse_args()
        config = generate_default_yaml(args.data) if not Path(args.data).exists() else load_yaml_config(args.data)
        final_config = {'batch': 16}  # 默认值
        final_config.update(config)  # YAML 覆盖默认值
        final_config.update({k: v for k, v in vars(args).items() if v is not None})  # CLI 覆盖
        for k, v in final_config.items():
            source = 'CLI' if k in vars(args) and vars(args)[k] is not None else 'YAML' if k in config else '默认值'
            logging.info(f'- {k}: {v} (来源: {source})')
        return final_config
    ```

#### FR-005：设备信息（system_utils.py）（优先级：中，已实现）
- **开发理由**：
  - 记录硬件环境（如 OS、CPU、GPU、PyTorch 版本）支持实验复现，方便调试硬件相关问题。
  - 满足审计需求，确保环境一致性。
- **实现思路**（已实现）：
  - 实现 `system_utils.get_device_info`，使用 `psutil` 获取 CPU/内存，`torch.cuda` 获取 GPU 信息。
  - 输出 JSON 格式，记录到日志（依赖 FR-000）。
  - 处理异常（如 `nvidia-smi` 不可用），回退到 CPU 模式。
  - 示例：
    ```python
    # utils/system_utils.py
    import psutil
    import torch
    import platform
    import json
    def get_device_info() -> dict:
        try:
            info = {
                'OS': {'Type': platform.system(), 'Version': platform.version()},
                'CPU': {'Cores': psutil.cpu_count(), 'Usage': psutil.cpu_percent()},
                'Memory': {'Total': f'{psutil.virtual_memory().total / 1e9:.2f} GB'},
                'GPU': {'Available': torch.cuda.is_available(), 'Count': torch.cuda.device_count()}
            }
            if info['GPU']['Available']:
                info['GPU']['Model'] = torch.cuda.get_device_name(0)
            logging.info(f'设备信息: {json.dumps(info, indent=2)}')
            return info
        except Exception as e:
            logging.warning(f'设备信息获取失败: {e}')
            return {'Errors': [str(e)]}
    ```

#### FR-006：数据集信息（dataset_utils.py）（优先级：中，已实现）
- **开发理由**：
  - 记录数据集信息（如类别数、样本数）确保数据一致性，支持实验复现和审计。
  - 验证数据集配置正确，减少验证错误。
- **实现思路**（已实现）：
  - 实现 `dataset_utils.get_dataset_info`，解析 `data.yaml`（依赖 FR-002）。
  - 使用 `pathlib.Path.glob` 统计 `val` 目录的样本数（`.jpg`, `.png`）。
  - 记录到日志（依赖 FR-000）。
  - 示例：
    ```python
    # utils/dataset_utils.py
    from pathlib import Path
    import yaml
    def get_dataset_info(yaml_path: str) -> dict:
        config = load_yaml_config(yaml_path)
        val_path = Path(config.get('val', 'images/val'))
        info = {
            'Config': yaml_path,
            'Classes': config.get('nc', 6),
            'Names': config.get('names', ['glioma-meningioma-pituitary-No', 'NO_tumor', 'glioma', 'meningioma', 'pituitary', 'space-occupying lesion']),
            'Val_Samples': len(list(val_path.glob('*.jpg'))) + len(list(val_path.glob('*.png')))
        }
        logging.info(f'数据集信息: {info}')
        return info
    ```

#### FR-007：组合信息（log_validation_info）（优先级：中）
- **开发理由**：
  - 整合设备（FR-005）、数据集（FR-006）和参数（FR-004）信息到日志，确保验证过程记录完整。
  - 支持实验复现和审计，简化调试。
- **实现思路**：
  - 实现 `log_validation_info`，调用 `get_device_info`（FR-005）、`get_dataset_info`（FR-006）和 `merge_configs`（FR-004）。
  - 格式化信息，记录到日志（依赖 FR-000）。
  - 示例：
    ```python
    import json
    def log_validation_info(config: dict, device_info: dict, dataset_info: dict):
        logging.info('===== 验证信息 =====')
        logging.info(f'参数: {config}')
        logging.info(f'设备: {json.dumps(device_info, indent=2)}')
        logging.info(f'数据集: {dataset_info}')
    ```

#### FR-008：模型拷贝（copy_checkpoint_models）（优先级：中，已实现）
- **开发理由**：
  - 拷贝验证模型到结果目录（如 `runs/val/validateN/weights`）确保实验完整性，便于版本管理和复现。
  - 支持审计，记录使用的模型文件。
- **实现思路**（已实现）：
  - 实现 `copy_checkpoint_models`，获取 `--weights` 参数，拷贝到 `runs/val/validateN/weights`。
  - 使用 `shutil.copy` 和 `pathlib.Path` 确保安全操作。
  - 示例：
    ```python
    from pathlib import Path
    import shutil
    def copy_checkpoint_models(weights: str, run_dir: str):
        target_dir = Path(run_dir) / 'weights'
        target_dir.mkdir(parents=True, exist_ok=True)
        target_path = target_dir / Path(weights).name
        shutil.copy(weights, target_path)
        logging.info(f'模型拷贝: {weights} -> {target_path}')
    ```

#### FR-009：主验证脚本（日志内容、错误处理、控制台输出）（优先级：高）
- **开发理由**：
  - 整合验证逻辑，记录详细日志内容（验证结果、耗时等），捕获异常（如 CUDA 错误），提供结果反馈。
  - 确保健壮性和用户体验，是核心功能。
- **实现思路**：
  - **日志内容**：记录验证开始/结束时间、结果（mAP@50、mAP@50:95等）、耗时，依赖 FR-000。
  - **错误处理**：使用 `try-except` 捕获异常（如 `torch.cuda.OutOfMemoryError`, `UnicodeEncodeError`），记录到日志。
  - **控制台输出**：打印验证指标（mAP@50、mAP@50:95等）和耗时。
  - 示例：
    ```python
    from ultralytics import YOLO
    import time
    import traceback
    import sys
    try:
        model = YOLO('yolov8n.pt')
        start_time = time.time()
        logging.info('验证开始')
        results = model.val(data='data.yaml')
        logging.info(f'验证结果: mAP@50={results.box.map50}, mAP@50:95={results.box.map}')
        logging.info(f'耗时: {time.time() - start_time:.2f}秒')
        print(f'验证结果: mAP@50={results.box.map50}, mAP@50:95={results.box.map}')
    except Exception as e:
        logging.error(f'验证失败: {e}\n{traceback.format_exc()}')
        sys.exit(1)
    ```

## 4. 非功能需求
- **NF-001：代码规范**：
  - 遵循 PEP 8，包含文档字符串，使用类型提示（如 `def load_yaml_config(yaml_path: str) -> dict`）。
- **NF-002：兼容性**：
  - 支持 Windows/Linux，GPU/CPU，YOLOv8/YOLOv11。
  - 处理中文路径（`utf-8-sig` 或 `utf-8` 编码）。
- **NF-003：性能**：
  - 日志记录不显著影响验证（如避免频繁 I/O）。
- **NF-004：可维护性**：
  - 模块化设计，复用 `system_utils.py`, `dataset_utils.py`, `logging_utils.py`。
- **NF-005：文档**：
  - 更新 README：新功能、使用示例（如 `python yolo_validate.py --data data.yaml --weights yolov8n.pt`）、依赖安装。
  - 测试报告：日志样本、结果目录（如 `runs/val/validate1`）、指标截图。
- **NF-006：安全性**：
  - 日志不包含敏感信息（如患者数据、API 密钥），检查 `os.environ` 和参数。
  - 使用 `pathlib` 确保安全文件操作。

## 5. 技术要求
- **编程语言**：Python 3.12+
- **依赖库**：
  - `ultralytics>=8.2.0`, `pyyaml>=6.0`, `psutil>=5.9.0`, `torch>=2.8.0`
  - 标准库：`logging`, `pathlib`, `argparse`, `shutil`
- **数据集**：YOLO 格式 `data.yaml`（如 `val: images/val, nc: 6, names: ['glioma-meningioma-pituitary-No', 'NO_tumor', 'glioma', 'meningioma', 'pituitary', 'space-occupying lesion']`）
- **模型**：`yolov8n.pt` 或训练后模型
- **硬件**：推荐 GPU（8GB，如 RTX 3070），最低 CPU（8核，16GB内存）
- **项目结构**：
  ```
  project/
  ├── yolo_validate.py
  ├── utils/
  │   ├── system_utils.py
  │   ├── dataset_utils.py
  │   ├── logging_utils.py
  ├── configs/
  │   └── data.yaml
  ├── logs/
  │   └── validate/
  ├── runs/
  │   └── val/
  └── pretrained_models/
      └── yolov8n.pt
  ```
- **版本控制**：Git 分支 `feature/tumor-validate-advanced`，提交格式如 `feat: add validation script`.

## 6. 验收标准
- **AC-001：日志功能**（高）：
  - 日志初始化为 `temp-YYYYMMDD-HHMMSS-yolov8n.log`，包含验证信息。
- **AC-002：日志重命名**（高）：
  - 重命名为 `validateN-YYYYMMDD_HHMMSS-yolov8n.log`，编号唯一。
- **AC-003：加载 YAML 配置**（高）：
  - 正确加载 `data.yaml`，记录路径和参数。
- **AC-004：生成默认 YAML**（高）：
  - 配置文件缺失时生成默认值（如 `nc: 6`, `names: ['glioma-meningioma-pituitary-No', 'NO_tumor', 'glioma', 'meningioma', 'pituitary', 'space-occupying lesion']`）。
- **AC-005：参数合并**（高）：
  - CLI 覆盖 YAML，YAML 覆盖默认值，记录来源。
- **AC-006：设备信息**（中）：
  - 日志包含 JSON 格式设备信息（OS、CPU、GPU）。
- **AC-007：数据集信息**（中）：
  - 日志记录类别数、样本数（如 `验证样本: 200`）。
- **AC-008：组合信息**（中）：
  - 日志整合设备、数据集、参数信息。
- **AC-009：模型拷贝**（中）：
  - 模型拷贝至 `runs/val/validateN/weights`。
- **AC-010：主验证脚本**（高）：
  - 记录验证结果（mAP@50、mAP@50:95）、耗时，捕获异常，输出指标。

## 7. 开发流程
- **流程**：
  1. **需求分析**（1天，6月15日）：
     - 确认模块优先级，聚焦高优先级（FR-009）。
  2. **代码开发**（3天，6月16-18日）：
     - 开发 FR-009（验证脚本），实现 FR-007（组合信息）。
     - 复用 FR-000-006、FR-008（日志、参数、设备、数据集、模型拷贝）。
  3. **功能测试**（2天，6月19-20日）：
     - 验证日志、参数、结果目录，检查 Windows/Linux、GPU/CPU。
  4. **文档编写**（1天，6月21日）：
     - 更新 README，提交测试报告。
  5. **提交审核**（1天，6月22日）：
     - 提交 Pull Request，配合审查。
- **时间表**：6月15-22日（7个工作日，含1天缓冲）。
- **职责**：
  - 开发：实现代码、文档。
  - 测试：验证功能、日志、结果。
  - 经理：审核进度。
  - 审核人：检查代码、文档。

## 8. 风险与缓解措施
- **R-001：日志重命名冲突**：
  - 检查 `logs/validate`，动态分配编号。
- **R-002：YAML 文件错误**：
  - 提供默认值，记录错误日志。
- **R-003：硬件信息获取失败**：
  - 回退 CPU 模式，记录警告。
- **R-004：中文路径编码问题**：
  - 使用 `utf-8` 或 `utf-8-sig`，捕获 `UnicodeEncodeError`。
- **R-005：参数合并错误**：
  - 验证 CLI > YAML > 默认值逻辑。
- **R-006：医学影像数据隐私**：
  - 确保日志和输出不包含患者敏感信息，检查 `data.yaml` 和数据集路径。
- **R-007：验证结果不一致**：
  - 验证数据集和模型一致性，记录详细指标。

## 9. 交付物
- **代码**：
  - `yolo_validate.py`, `utils/system_utils.py`, `utils/dataset_utils.py`, `utils/logging_utils.py`.
- **文档**：
  - README：新功能、使用示例、依赖安装。
  - 测试报告：日志样本、结果目录、指标截图。
- **版本控制**：
  - 提交至 `feature/tumor-validate-advanced`，示例：`feat: add validation script`.

## 10. 参考资源
- **代码**：现有 `yolo_train.py`, `utils/*.py`.
- **文档**：Ultralytics 文档（https://docs.ultralytics.com）。
- **数据集**：`configs/data.yaml`（肿瘤检测，200验证样本）。
- **模型**：`pretrained_models/yolov8n.pt` 或训练后模型。
- **环境**：Windows 11/Ubuntu 22.04，Python 3.12，RTX 3070。

## 11. 审批流程
1. 提交 Pull Request 至 `feature/tumor-validate-advanced`。
2. 审核代码（PEP 8、功能实现）、文档、测试报告。
3. 测试团队验证日志、结果、指标。
4. 经理批准合并至 `main`。
5. 存档文档至项目库。