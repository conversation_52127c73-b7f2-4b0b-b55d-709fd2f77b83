# YOLOv8/YOLOv11 推理脚本开发需求（进阶版）

## 文档信息
- **文档编号**：REQ-YOLO-TUMOR-INFER-002
- **版本**：1.0
- **作者**：雨霓
- **创建日期**：2025年6月15日
- **更新日期**：2025年6月15日 12:33 AM AEST
- **状态**：草稿
- **审核人**：待定
- **分发范围**：开发团队、测试团队、项目经理

## 1. 项目背景
本项目为医学影像分割检测系统的一部分，基于 Ultralytics YOLOv8/YOLOv11 模型，开发进阶推理脚本（`yolo_infer.py`），用于对医学图像、视频、摄像头流等输入进行肿瘤检测（包括 glioma、meningioma、pituitary 等）。基础推理脚本（`yolo_infer.py` v0）已实现基本功能，支持单张图像、图像文件夹和视频的检测，输出带检测框的结果。本需求文档定义进阶推理脚本的功能，新增支持摄像头流、动态美化输出、日志记录优化、YAML 配置支持等功能，满足复杂的医学影像检测场景，提升用户体验和系统健壮性。

## 2. 目标
- **业务目标**：
  - 提供高效、灵活的推理脚本，支持多种输入格式（图像、文件夹、视频、摄像头流）。
  - 提供可视化输出（带美化检测框的图像/视频），便于医学专业人员分析。
  - 支持 YAML 配置，简化参数管理。
- **技术目标**：
  - 实现流式推理（摄像头/视频），优化性能和资源使用。
  - 集成美化输出模块（圆角标签、中文支持），提升结果展示效果。
  - 增强日志记录，包含设备信息、推理时间、参数来源等。
  - 支持动态参数调整（如美化参数随分辨率自适应）。
  - 提供完善的错误处理和路径标准化。
- **交付目标**：
  - 交付进阶版 `yolo_infer.py`（v6），支持所有功能。
  - 提供开发文档（README）、测试报告和 Git 提交。

## 3. 功能需求
### 3.1 输入支持
- **FR-001：输入格式**
  - 支持单张图像、图像文件夹、视频文件和摄像头流。
  - 支持图像格式：`.jpg`, `.png`, `.jpeg`, `.bmp`, `.webp`。
  - 支持视频格式：`.mp4`, `.avi`, `.mov`。
  - 支持摄像头输入：通过 ID（如 `0` 表示默认摄像头）。
  - 输入参数：`--source`（默认 `0`，表示默认摄像头）。
- **FR-002：路径标准化**
  - 自动将相对路径转换为绝对路径（`--source`, `--weights`）。
  - 默认权重路径：`models/checkpoints/trainN-时间戳-yolov8n-best.pt`。
  - 默认保存路径：`runs/predict/expN`（`N` 自动递增）。

### 3.2 模型推理
- **FR-003：模型加载**
  - 加载训练好的 YOLOv8/YOLOv11 模型权重（`.pt` 文件）。
  - 默认权重：`train6-20250615-002700-yolov8n-best.pt`。
  - 输入参数：`--weights`。
- **FR-004：推理参数**
  - 支持核心推理参数：
    - `--imgsz`：输入图像尺寸（默认 640）。
    - `--conf`：置信度阈值（默认 0.25）。
    - `--iou`：IOU 阈值（默认 0.45）。
    - `--device`：计算设备（默认 `None`，自动选择 GPU/CPU）。
  - 支持额外参数（通过 `--extra_args` 传递，如 `--max_det 1000`）。
- **FR-005：流式推理**
  - 支持摄像头和视频的流式推理，优化帧处理效率。
  - 避免逐帧生成独立文件夹，统一保存到 `runs/predict/expN`。
  - 支持用户通过 `q` 或 `Esc` 键退出摄像头推理。

### 3.3 结果保存
- **FR-006：结果目录**
  - 保存推理结果到 `runs/predict/expN`（`N` 自动递增）。
  - 图像输出：带检测框的 `.jpg` 文件。
  - 视频输出：带检测框的 `.mp4` 文件。
  - 支持帧图像保存：`--save_frame`（默认 `True`），保存到 `expN/0_frames`。
  - 支持检测框裁剪：`--save_crop`（默认 `True`）。
  - 支持标签保存：`--save_txt`（默认 `True`），`--save_conf`（默认 `True`）。
- **FR-007：显示控制**
  - 支持动态显示分辨率：`--display-size`（选项：`360p`, `720p`, `1280p`, `2K`, `4K`；默认 `1280p`）。
  - 显示窗口标题：`YOLO Tumor Detection`。

### 3.4 日志记录
- **FR-008：日志初始化**
  - 初始化日志记录器，文件名 `temp-时间戳-yolov8n-best.log`。
  - 保存到 `base_path/logging/infer`。
  - 输入参数：`--log_encoding`（默认 `utf-8-sig`），`--log_level`（默认 `INFO`）。
- **FR-009：日志重命名**
  - 重命名日志为 `inferN-时间戳-yolov8n-best.log`，与结果目录 `expN` 一致。
  - 调用 `rename_log_file`（复用 `utils.logging_utils.py`）。
- **FR-010：日志内容**
  - 记录以下信息：
    - 设备信息：操作系统、CPU/GPU、显存（如 `Windows-11, NVIDIA RTX 3070 (8GB)`）。
    - 输入信息：路径、样本数（图像数或视频帧数）。
    - 模型信息：权重文件名、模型参数。
    - 参数来源：CLI 或 YAML，记录被覆盖的参数。
    - 推理结果：检测框总数、主要类别统计（如 `glioma=2, meningioma=1`）。
    - 耗时：总时间、单帧平均时间（预处理、推理、后处理）。
    - 结果目录：`runs/predict/expN`。
  - 示例日志：
    ```
    2025-06-15 00:33:05,123 - YOLO_Inference - INFO - 推理开始
    2025-06-15 00:33:05,124 - YOLO_Inference - INFO - 设备信息: OS=Windows-11-10.0.26100-SP0, GPU=NVIDIA RTX 3070 (8.00 GB)
    2025-06-15 00:33:05,125 - YOLO_Inference - INFO - 输入信息: 路径=0 (摄像头), 样本数=流式
    2025-06-15 00:33:05,126 - YOLO_Inference - INFO - 模型信息: train6-20250615-002700-yolov8n-best.pt
    2025-06-15 00:33:05,127 - YOLO_Inference - INFO - 参数来源: CLI(imgsz=640, conf=0.25), YAML(iou=0.45)
    2025-06-15 00:33:10,456 - YOLO_Inference - INFO - 推理结果: 检测框总数=10, glioma=5, meningioma=3, pituitary=2
    2025-06-15 00:33:10,457 - YOLO_Inference - INFO - 耗时: 总帧数=100, 总预处理=50.00ms, 总推理=300.00ms, 总后处理=20.00ms
    2025-06-15 00:33:10,458 - YOLO_Inference - INFO - 单帧平均时间: 预处理=0.50ms, 推理=3.00ms, 后处理=0.20ms
    2025-06-15 00:33:10,459 - YOLO_Inference - INFO - 结果目录: runs/predict/exp1
    2025-06-15 00:33:10,460 - YOLO_Inference - INFO - 日志重命名: infer1-20250615_003310-yolov8n-best.log
    ```

### 3.5 美化输出
- **FR-011：美化检测框**
  - 支持美化绘制：圆角标签、中文标签映射（`--use-chinese`, 默认 `True`）。
  - 美化参数（以 720p 为基准，动态调整）：
    - `--font-size`：字体大小（默认 22）。
    - `--line-width`：线宽（默认 4）。
    - `--label-padding-x`, `--label-padding-y`：标签内边距（默认 30, 18）。
    - `--radius`：圆角半径（默认 8）。
  - 动态调整：根据帧尺寸（最小边/720）缩放参数，限制范围：
    - 字体大小：12-60 像素。
    - 线宽：2-10 像素。
    - 内边距：水平 10-60 像素，垂直 6-36 像素。
    - 圆角半径：4-20 像素。
  - 复用 `utils.beautify.custom_plot` 函数。
- **FR-012：中文标签映射**
  - 类别映射：`glioma-meningioma-pituitary-No` -> `无肿瘤类型`, `NO_tumor` -> `无肿瘤`, `glioma` -> `胶质瘤`, `meningioma` -> `脑膜瘤`, `pituitary` -> `垂体瘤`, `space-occupying lesion` -> `占位性病变`。
  - 可通过 `--use-chinese=False` 禁用。

### 3.6 YAML 配置支持
- **FR-013：参数加载**
  - 支持从 `configs/infer.yaml` 加载配置，优先级低于命令行参数。
  - 输入参数：`--use_yaml`（默认 `True`）。
  - 复用 `utils.load_yaml_config` 和 `merge_configs`（支持 `infer` 模式）。
- **FR-014：参数合并**
  - 合并命令行和 YAML 参数，记录覆盖情况（如 `CLI(imgsz=640) 覆盖 YAML(imgsz=1280)`）。
  - 支持额外参数（`--extra_args`）。

### 3.7 控制台输出
- **FR-015：核心指标输出**
  - 显示检测框总数和主要类别统计。
  - 显示推理时间（总时间、单帧平均时间：预处理、推理、后处理）。
  - 示例：
    ```
    推理结果：
    - 检测框总数: 10
    - 胶质瘤: 5
    - 脑膜瘤: 3
    - 垂体瘤: 2
    - 耗时: 总帧数=100, 总预处理=50.00ms, 总推理=300.00ms, 总后处理=20.00ms
    - 单帧平均时间: 预处理=0.50ms, 推理=3.00ms, 后处理=0.20ms
    ```

### 3.8 错误处理
- **FR-016：文件检查**
  - 检查 `--source` 和 `--weights` 是否存在，抛出 `FileNotFoundError`。
  - 检查视频/摄像头是否可打开，抛出 `RuntimeError`。
- **FR-017：异常捕获**
  - 捕获推理、文件操作等异常，记录到日志并抛出。
- **FR-018：参数验证**
  - 验证 `--imgsz`（正整数，32 的倍数）、`--conf`、`--iou`（0-1 范围）等。

## 4. 非功能需求
- **NF-001：代码规范**：
  - 遵循 PEP 8，包含详细文档字符串和注释。
  - 函数模块化，复用 `utils.logging_utils.py`, `utils.dataset_utils.py`, `utils.beautify.py`.
- **NF-002：兼容性**：
  - 支持 Windows/Linux，GPU/CPU。
  - 支持 Python 3.12+，Ultralytics YOLOv8/YOLOv11。
- **NF-003：性能**：
  - 单张图像推理时间 < 1 秒（RTX 3070）。
  - 流式推理帧率 > 20 FPS（720p，RTX 3070）。
  - 日志记录开销 < 0.1 秒。
- **NF-004：可维护性**：
  - 模块化设计，支持扩展（如新输入格式）。
  - 清晰的日志和错误信息，便于调试。
- **NF-005：文档**：
  - 提供详细 README，包含使用示例、参数说明、依赖安装。
  - 提供测试报告，覆盖图像、视频、摄像头场景。
- **NF-006：安全性**：
  - 日志不包含敏感信息（如患者数据、API 密钥），检查 `os.environ` 和参数。
  - 使用 `pathlib` 确保安全文件操作。

## 5. 技术要求
- **语言**：Python 3.12+。
- **依赖**：`ultralytics>=8.2.0`, `pyyaml>=6.0`, `opencv-python`, 标准库（`logging`, `pathlib`, `argparse`）。
- **数据集**：`configs/data.yaml`（类别信息），输入图像/视频/摄像头。
- **硬件**：单 GPU（8GB）或 CPU，推荐 RTX 3070。
- **结构**：
  - 主脚本：`yolo_infer.py`。
  - 辅助模块：`utils/logging_utils.py`, `utils/dataset_utils.py`, `utils/beautify.py`.
  - 配置：`configs/infer.yaml`.
- **版本控制**：Git，分支 `feature/tumor-infer-advanced`.

## 6. 验收标准
- **AC-001：输入支持**：
  - 支持单张图像、图像文件夹、视频、摄像头流。
  - 正确处理多种格式（`.jpg`, `.png`, `.mp4`, 等）。
- **AC-002：模型推理**：
  - 正确加载权重，执行推理（图像/视频/流）。
  - 支持流式推理，帧率 > 20 FPS（720p，RTX 3070）。
- **AC-003：结果保存**：
  - 保存到 `runs/predict/expN`，包含图像、视频、帧、裁剪、标签。
  - 目录编号自动递增。
- **AC-004：美化输出**：
  - 支持圆角标签、中文映射，参数随分辨率动态调整。
  - 美化效果与 `utils.beautify.custom_plot` 一致。
- **AC-005：日志记录**：
  - 日志文件 `inferN-时间戳-yolov8n-best.log`，包含设备、输入、模型、参数、结果、耗时。
  - 日志重命名与结果目录一致。
- **AC-006：YAML 配置**：
  - 正确加载 `infer.yaml`，合并 CLI 和 YAML 参数。
  - 记录参数覆盖情况。
- **AC-007：控制台输出**：
  - 显示检测框总数、类别统计、推理时间。
- **AC-008：错误处理**：
  - 检查文件/设备存在性，抛出适当异常。
  - 捕获所有异常，记录详细日志。
- **AC-009：代码质量**：
  - 符合 PEP 8，无 bug，模块化设计。
- **AC-010：文档与测试**：
  - 提供 README（使用示例、参数说明）。
  - 提供测试报告，覆盖图像、视频、摄像头场景。

## 7. 开发流程
- **流程**：
  1. 需求分析，确认功能点（1天，6月15日）。
  2. 开发 `yolo_infer.py`，集成 `utils/logging_utils.py`, `utils/dataset_utils.py`, `utils/beautify.py`（3天，6月16-18日）。
  3. 测试图像、视频、摄像头输入，验证美化、日志功能（2天，6月19-20日）。
  4. 编写 README 和测试报告（1天，6月21日）。
  5. 提交 Pull Request（1天，6月22日）。
- **时间**：2025年6月15日 - 6月22日（7 个工作日，含1天缓冲）。
- **职责**：
  - 开发：实现代码、模块、文档。
  - 测试：验证功能、性能、兼容性。
  - 经理：审核进度、代码、文档。

## 8. 风险与缓解措施
- **R-001：输入格式兼容性**：
  - **风险**：不同格式的图像/视频可能导致解析失败。
  - **缓解**：限制支持格式，明确记录错误信息。
- **R-002：流式推理性能**：
  - **风险**：高分辨率视频/摄像头可能导致帧率不足。
  - **缓解**：优化推理参数（如 `--imgsz`），推荐 720p/1280p。
- **R-003：日志重命名冲突**：
  - **风险**：多任务运行可能导致日志文件冲突。
  - **缓解**：检查目录，自动递增编号。
- **R-004：医学影像数据隐私**：
  - **风险**：日志或输出可能包含患者敏感信息。
  - **缓解**：检查 `data.yaml` 和数据集路径，确保日志不包含敏感信息。
- **R-005：进度延误**：
  - **风险**：功能复杂可能导致开发超时。
  - **缓解**：优先实现核心功能（推理、保存、日志），美化作为次优先级。

## 9. 交付物
- **代码**：
  - 主脚本：`yolo_infer.py`。
  - 辅助模块：`utils/logging_utils.py`, `utils/dataset_utils.py`, `utils/beautify.py`.
  - 测试脚本：`test_beautify.py`.
- **配置**：`configs/infer.yaml`.
- **文档**：README（使用说明、参数、依赖）、测试报告。
- **Git 提交**：分支 `feature/tumor-infer-advanced`，包含清晰提交记录。

## 10. 参考资源
- **代码**：`yolo_validate.py`, `utils/logging_utils.py`, `utils/dataset_utils.py`, `utils/beautify.py`.
- **文档**：Ultralytics 文档（https://docs.ultralytics.com）。
- **数据集**：`configs/data.yaml`，测试医学图像/视频，摄像头。
- **环境**：Windows 11/Ubuntu 22.04，Python 3.12，RTX 3070。

## 11. 审批流程
- 提交 Pull Request（分支 `feature/tumor-infer-advanced`）。
- 审核代码、功能、文档、测试结果。
- 合并到 `main` 分支，存档文档。
- 归档需求文档和测试报告。