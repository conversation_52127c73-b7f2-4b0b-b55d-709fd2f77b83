{% extends 'base.html' %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <!-- 页面标题 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>
                <i class="fas fa-chart-line"></i> 检测结果 #{{ record.id }}
            </h2>
            <div>
                <a href="{% url 'index' %}" class="btn btn-outline-primary">
                    <i class="fas fa-plus"></i> 新检测
                </a>
                <a href="{% url 'history' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-list"></i> 历史记录
                </a>
            </div>
        </div>
        
        <!-- 状态信息 -->
        {% if record.status == 'completed' %}
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> 检测完成！
                处理时间: {{ record.processing_time|floatformat:2 }} 秒
            </div>
        {% elif record.status == 'failed' %}
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i> 检测失败: {{ record.error_message }}
            </div>
        {% else %}
            <div class="alert alert-info">
                <i class="fas fa-spinner fa-spin"></i> 正在处理中...
            </div>
        {% endif %}
    </div>
</div>

{% if record.status == 'completed' %}
<div class="row">
    <!-- 图片对比 -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-images"></i> 检测结果对比
                </h5>
            </div>
            <div class="card-body">
                <div class="result-comparison">
                    <!-- 原始图片 -->
                    <div class="flex-fill">
                        <h6 class="text-center mb-3">原始图片</h6>
                        <div class="text-center">
                            <img src="{{ record.original_image.url }}" 
                                 class="img-fluid rounded shadow" 
                                 alt="原始图片"
                                 style="max-height: 400px;">
                        </div>
                    </div>
                    
                    <!-- 检测结果 -->
                    <div class="flex-fill">
                        <h6 class="text-center mb-3">检测结果</h6>
                        <div class="text-center">
                            {% if record.result_image %}
                                <img src="{{ record.result_image.url }}" 
                                     class="img-fluid rounded shadow" 
                                     alt="检测结果"
                                     style="max-height: 400px;">
                            {% else %}
                                <div class="bg-light rounded d-flex align-items-center justify-content-center" 
                                     style="height: 300px;">
                                    <div class="text-center text-muted">
                                        <i class="fas fa-image fa-3x mb-3"></i>
                                        <p>暂无结果图片</p>
                                    </div>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 大模型API分析模块 -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-robot"></i> AI智能分析
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <!-- 左侧：输入区域 -->
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="llmPrompt" class="form-label">
                                <i class="fas fa-comment-dots"></i> 分析提示词
                            </label>
                            <textarea class="form-control llm-prompt-textarea"
                                      id="llmPrompt"
                                      rows="3"
                                      placeholder="请输入您想要AI分析的问题，例如：
• 分析这张图片中的口罩佩戴情况
• 给出口罩佩戴的改进建议
• 评估整体的防疫合规性">基于检测结果，请分析这张图片中的口罩佩戴情况，并给出专业的评估和建议。</textarea>
                        </div>

                        <div class="mb-3">
                            <label for="llmModel" class="form-label">
                                <i class="fas fa-brain"></i> 选择AI模型
                            </label>
                            <select class="form-select llm-model-select" id="llmModel">
                                <option value="Qwen/QwQ-32B" selected>Qwen QwQ-32B (强大推理)</option>
                                <option value="Qwen/Qwen2.5-7B-Instruct">Qwen 2.5-7B-Instruct (快速响应)</option>
                                <option value="Qwen/Qwen2.5-14B-Instruct">Qwen 2.5-14B-Instruct (平衡性能)</option>
                                <option value="deepseek-ai/DeepSeek-V2.5">DeepSeek V2.5 (深度思考)</option>
                            </select>
                        </div>

                        <div class="d-grid">
                            <button type="button"
                                    class="btn btn-primary llm-analysis-btn"
                                    id="startLLMAnalysis"
                                    onclick="startLLMAnalysis()">
                                <i class="fas fa-magic"></i> 开始AI分析
                            </button>
                        </div>
                    </div>

                    <!-- 右侧：结果显示区域 -->
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">
                                <i class="fas fa-lightbulb"></i> AI分析结果
                            </label>
                        </div>

                        <!-- 加载状态 -->
                        <div id="llmLoadingState" class="text-center py-4 llm-loading-animation" style="display: none;">
                            <div class="spinner-border text-primary mb-3" role="status">
                                <span class="visually-hidden">分析中...</span>
                            </div>
                            <p class="text-muted">AI正在分析中，请稍候...</p>
                            <div class="progress">
                                <div class="progress-bar progress-bar-striped progress-bar-animated"
                                     role="progressbar"
                                     style="width: 100%"></div>
                            </div>
                        </div>

                        <!-- 结果显示 -->
                        <div id="llmResultArea" class="llm-result-area" style="min-height: 180px;">
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-robot fa-2x mb-3 ai-icon-animated"></i>
                                <p>点击"开始AI分析"按钮，获取专业的智能分析结果</p>
                            </div>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="mt-3 llm-action-buttons d-flex gap-2">
                            <button type="button"
                                    class="btn btn-outline-secondary btn-sm"
                                    id="copyLLMResult"
                                    onclick="copyLLMResult()"
                                    style="display: none;">
                                <i class="fas fa-copy"></i> 复制结果
                            </button>
                            <button type="button"
                                    class="btn btn-outline-info btn-sm"
                                    id="downloadLLMResult"
                                    onclick="downloadLLMResult()"
                                    style="display: none;">
                                <i class="fas fa-download"></i> 下载分析报告
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 统计信息 -->
    <div class="col-lg-4">
        <!-- 检测摘要 -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-chart-pie"></i> 检测摘要
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center mb-3">
                    <div class="col-12">
                        <h3 class="text-primary">{{ record.total_detections }}</h3>
                        <small class="text-muted">总检测数量</small>
                    </div>
                </div>
                
                <div class="row text-center">
                    <div class="col-4">
                        <div class="border-end">
                            <h5 class="text-danger">{{ record.glioma_tumor_count }}</h5>
                            <small class="text-muted">胶质瘤</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="border-end">
                            <h5 class="text-warning">{{ record.meningioma_tumor_count }}</h5>
                            <small class="text-muted">脑膜瘤</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <h5 class="text-info">{{ record.pituitary_tumor_count }}</h5>
                        <small class="text-muted">垂体瘤</small>
                    </div>
                </div>
                
                <hr>
                

                
                <div class="progress">
                    <div class="progress-bar bg-success" 
                         style="width: {{ detection_summary.compliance_rate }}%"></div>
                </div>
            </div>
        </div>
        
        <!-- 检测参数 -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-cog"></i> 检测参数
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-2">
                    <strong>模型:</strong> {{ record.model_name }}
                </div>
                <div class="mb-2">
                    <strong>置信度:</strong> {{ record.confidence_threshold }}
                </div>
                <div class="mb-2">
                    <strong>IOU阈值:</strong> {{ record.iou_threshold }}
                </div>
                <div class="mb-2">
                    <strong>图像尺寸:</strong> {{ record.image_size }}x{{ record.image_size }}
                </div>
                <div class="mb-2">
                    <strong>检测时间:</strong> {{ record.upload_time|date:"Y-m-d H:i:s" }}
                </div>
            </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="card">
            <div class="card-body">
                <div class="d-grid gap-2">
                    {% if record.result_image %}
                    <a href="{{ record.result_image.url }}" 
                       class="btn btn-success" 
                       download="detection_result_{{ record.id }}.png">
                        <i class="fas fa-download"></i> 下载结果图片
                    </a>
                    {% endif %}
                    
                    <button class="btn btn-info" onclick="showDetails()">
                        <i class="fas fa-info-circle"></i> 查看详细数据
                    </button>
                    
                    <form method="post" action="{% url 'delete_record' record.id %}" 
                          onsubmit="return confirm('确定要删除这条记录吗？');">
                        {% csrf_token %}
                        <button type="submit" class="btn btn-outline-danger w-100">
                            <i class="fas fa-trash"></i> 删除记录
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>


<!-- 详细检测数据模态框 -->
<div class="modal fade" id="detailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-list-alt"></i> 详细检测数据
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                {% if record.detection_details %}
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>序号</th>
                                <th>类别</th>
                                <th>置信度</th>
                                <th>位置 (x, y)</th>
                                <th>尺寸 (w, h)</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for detection in record.detection_details %}
                            <tr>
                                <td>{{ forloop.counter }}</td>
                                <td>
                                    {% if detection.class_name == 'glioma_tumor' %}
                                        <span class="badge bg-danger">胶质瘤</span>
                                    {% elif detection.class_name == 'meningioma_tumor' %}
                                        <span class="badge bg-warning">脑膜瘤</span>
                                    {% elif detection.class_name == 'pituitary_tumor' %}
                                        <span class="badge bg-info">垂体瘤</span>
                                    {% else %}
                                        <span class="badge bg-secondary">{{ detection.class_name }}</span>
                                    {% endif %}
                                </td>
                                <td>{{ detection.confidence|floatformat:3 }}</td>
                                <td>{{ detection.x_center|floatformat:3 }}, {{ detection.y_center|floatformat:3 }}</td>
                                <td>{{ detection.width|floatformat:3 }}, {{ detection.height|floatformat:3 }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted text-center">暂无详细检测数据</p>
                {% endif %}
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
function showDetails() {
    $('#detailsModal').modal('show');
}

// 如果状态不是完成，定期检查状态
{% if record.status != 'completed' and record.status != 'failed' %}
function checkStatus() {
    $.get('{% url "detection_status" record.id %}', function(data) {
        if (data.status === 'completed' || data.status === 'failed') {
            location.reload();
        }
    });
}

// 每3秒检查一次状态
setInterval(checkStatus, 3000);
{% endif %}

// 大模型API相关功能
let llmAnalysisInProgress = false;
let llmResultContent = '';

// 开始LLM分析
function startLLMAnalysis() {
    if (llmAnalysisInProgress) {
        return;
    }

    const prompt = $('#llmPrompt').val().trim();
    const model = $('#llmModel').val();

    if (!prompt) {
        alert('请输入分析提示词');
        return;
    }

    // 设置加载状态
    setLLMLoadingState(true);

    // 准备请求数据
    const requestData = {
        prompt: prompt,
        model: model,
        record_id: {{ record.id }},
        detection_data: {
            total_detections: {{ record.total_detections }},
            with_mask_count: {{ record.with_mask_count }},
            without_mask_count: {{ record.without_mask_count }},
            incorrect_mask_count: {{ record.incorrect_mask_count }},
            compliance_rate: {{ detection_summary.compliance_rate }},
            model_name: '{{ record.model_name }}',
            confidence_threshold: {{ record.confidence_threshold }},
            detection_details: {{ record.detection_details|safe }}
        }
    };

    // 发送API请求
    $.ajax({
        url: '/api/llm-analysis/',
        type: 'POST',
        data: JSON.stringify(requestData),
        contentType: 'application/json',
        headers: {
            'X-CSRFToken': $('[name=csrfmiddlewaretoken]').val()
        },
        timeout: 60000, // 60秒超时
        success: function(response) {
            if (response.success) {
                displayLLMResult(response.analysis, response.model_used, response.api_provider);
                llmResultContent = response.analysis;
            } else {
                displayLLMError(response.error || '分析失败，请重试');
            }
        },
        error: function(xhr, status, error) {
            let errorMsg = '网络错误，请检查连接后重试';
            if (xhr.responseJSON && xhr.responseJSON.error) {
                errorMsg = xhr.responseJSON.error;
            } else if (status === 'timeout') {
                errorMsg = '请求超时，请重试';
            }
            displayLLMError(errorMsg);
        },
        complete: function() {
            setLLMLoadingState(false);
        }
    });
}

// 设置加载状态
function setLLMLoadingState(loading) {
    llmAnalysisInProgress = loading;
    const $button = $('#startLLMAnalysis');
    const $loadingState = $('#llmLoadingState');
    const $resultArea = $('#llmResultArea');

    if (loading) {
        $button.prop('disabled', true)
               .html('<i class="fas fa-spinner fa-spin"></i> 分析中...');
        $loadingState.show();
        $resultArea.hide();
    } else {
        $button.prop('disabled', false)
               .html('<i class="fas fa-magic"></i> 开始AI分析');
        $loadingState.hide();
        $resultArea.show();
    }
}

// 显示LLM分析结果
function displayLLMResult(analysis, modelUsed, apiProvider) {
    const $resultArea = $('#llmResultArea');
    const $copyBtn = $('#copyLLMResult');
    const $downloadBtn = $('#downloadLLMResult');

    // 格式化分析结果
    const formattedAnalysis = formatAnalysisResult(analysis);

    // 构建模型信息显示
    let modelInfo = '';
    if (modelUsed && apiProvider) {
        const providerIcon = apiProvider === 'SiliconFlow' ? 'fas fa-microchip' : 'fas fa-robot';
        const providerColor = apiProvider === 'SiliconFlow' ? 'text-primary' : 'text-info';
        modelInfo = `
            <div class="mb-2">
                <small class="text-muted">
                    <i class="${providerIcon} ${providerColor}"></i>
                    ${apiProvider} - ${modelUsed}
                </small>
            </div>
        `;
    }

    $resultArea.html(`
        <div class="llm-result-success">
            <div class="d-flex align-items-center mb-2">
                <i class="fas fa-check-circle text-success me-2"></i>
                <strong>AI分析完成</strong>
            </div>
            ${modelInfo}
            <div class="llm-result-content">${formattedAnalysis}</div>
        </div>
    `).addClass('has-content');

    // 显示操作按钮
    $copyBtn.show();
    $downloadBtn.show();
}

// 显示LLM错误信息
function displayLLMError(error) {
    const $resultArea = $('#llmResultArea');

    $resultArea.html(`
        <div class="llm-result-warning">
            <div class="d-flex align-items-center mb-2">
                <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                <strong>分析失败</strong>
            </div>
            <div class="text-muted mb-3">${error}</div>
            <div class="d-flex gap-2 mb-2">
                <button type="button"
                        class="btn btn-outline-primary btn-sm"
                        onclick="retryLLMAnalysis()">
                    <i class="fas fa-redo"></i> 重试分析
                </button>
                <button type="button"
                        class="btn btn-outline-secondary btn-sm"
                        onclick="clearLLMResult()">
                    <i class="fas fa-times"></i> 清除
                </button>
            </div>
            <div class="mt-2">
                <small class="text-muted">
                    <i class="fas fa-lightbulb"></i>
                    建议：检查网络连接或稍后重试，如果问题持续请联系管理员
                </small>
            </div>
        </div>
    `).removeClass('has-content');
}

// 重试LLM分析
function retryLLMAnalysis() {
    if (llmAnalysisInProgress) {
        return;
    }

    // 清除之前的错误信息
    $('#llmResultArea').empty();

    // 重新开始分析
    startLLMAnalysis();
}

// 清除LLM结果
function clearLLMResult() {
    $('#llmResultArea').empty().removeClass('has-content');
    llmResultContent = '';
}

// 格式化分析结果
function formatAnalysisResult(analysis) {
    // 将换行符转换为HTML换行
    let formatted = analysis.replace(/\n/g, '<br>');

    // 高亮关键词
    const keywords = ['正确佩戴', '未佩戴', '错误佩戴', '建议', '总结', '分析', '合规率'];
    keywords.forEach(keyword => {
        const regex = new RegExp(`(${keyword})`, 'gi');
        formatted = formatted.replace(regex, '<strong class="text-primary">$1</strong>');
    });

    return formatted;
}

// 复制LLM结果
function copyLLMResult() {
    if (!llmResultContent) {
        alert('暂无结果可复制');
        return;
    }

    // 创建临时文本区域
    const textArea = document.createElement('textarea');
    textArea.value = llmResultContent;
    document.body.appendChild(textArea);
    textArea.select();

    try {
        document.execCommand('copy');
        // 显示成功提示
        const $btn = $('#copyLLMResult');
        const originalText = $btn.html();
        $btn.html('<i class="fas fa-check"></i> 已复制').addClass('btn-success').removeClass('btn-outline-secondary');

        setTimeout(() => {
            $btn.html(originalText).removeClass('btn-success').addClass('btn-outline-secondary');
        }, 2000);
    } catch (err) {
        alert('复制失败，请手动选择文本复制');
    }

    document.body.removeChild(textArea);
}

// 下载LLM分析报告
function downloadLLMResult() {
    if (!llmResultContent) {
        alert('暂无结果可下载');
        return;
    }

    // 创建完整的分析报告
    const reportContent = `
脑肿瘤检测AI分析报告
==================

检测记录ID: {{ record.id }}
检测时间: {{ record.upload_time|date:"Y-m-d H:i:s" }}
使用模型: {{ record.model_name }}

检测结果统计:
- 总检测数量: {{ record.total_detections }}
- 胶质瘤检出: {{ record.glioma_tumor_count }}
- 脑膜瘤检出: {{ record.meningioma_tumor_count }}
- 垂体瘤检出: {{ record.pituitary_tumor_count }}

AI智能分析:
${llmResultContent}

报告生成时间: ${new Date().toLocaleString('zh-CN')}
    `.trim();

    // 创建下载链接
    const blob = new Blob([reportContent], { type: 'text/plain;charset=utf-8' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `脑肿瘤检测AI分析报告_${new Date().getTime()}.txt`;

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
}

// 页面加载完成后的初始化
$(document).ready(function() {
    // 为提示词输入框添加字符计数
    $('#llmPrompt').on('input', function() {
        const length = $(this).val().length;
        const maxLength = 1000;

        if (length > maxLength * 0.8) {
            $(this).addClass('border-warning');
        } else {
            $(this).removeClass('border-warning');
        }
    });

    // 模型选择变化时的提示
    $('#llmModel').on('change', function() {
        const model = $(this).val();
        let tip = '';

        switch(model) {
            case 'Qwen/QwQ-32B':
                tip = '强大的推理模型，适合复杂分析';
                break;
            case 'Qwen/Qwen2.5-7B-Instruct':
                tip = '快速响应，适合一般分析';
                break;
            case 'Qwen/Qwen2.5-14B-Instruct':
                tip = '平衡性能，综合分析能力强';
                break;
            case 'deepseek-ai/DeepSeek-V2.5':
                tip = '深度思考模型，适合专业分析';
                break;
        }

        // 可以在这里显示模型提示信息
        console.log('选择模型:', model, '-', tip);
    });
});
</script>
{% endblock %}
