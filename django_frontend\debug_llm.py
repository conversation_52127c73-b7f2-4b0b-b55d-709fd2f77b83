#!/usr/bin/env python
"""
LLM API调试脚本
用于诊断AI分析功能的网络连接问题
"""
import os
import sys
import django
import requests
import json
from pathlib import Path

# 设置Django环境
sys.path.append(str(Path(__file__).parent))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'brain_tumor_detection.settings')
django.setup()

from llm_config import SILICONFLOW_CONFIG, AVAILABLE_MODELS, DEFAULT_MODEL
from detection.api_views import call_llm_api, build_analysis_context

def test_network_connection():
    """测试网络连接"""
    print("🌐 测试网络连接...")
    
    try:
        # 测试基本网络连接
        response = requests.get('https://www.baidu.com', timeout=5)
        if response.status_code == 200:
            print("✅ 基本网络连接正常")
        else:
            print("❌ 基本网络连接异常")
            return False
    except Exception as e:
        print(f"❌ 网络连接失败: {e}")
        return False
    
    return True

def test_siliconflow_api():
    """测试SiliconFlow API"""
    print("\n🤖 测试SiliconFlow API...")
    
    api_key = SILICONFLOW_CONFIG['api_key']
    base_url = SILICONFLOW_CONFIG['base_url']
    
    print(f"API密钥: {api_key[:10]}...{api_key[-10:]}")
    print(f"API地址: {base_url}")
    
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json'
    }
    
    payload = {
        'model': DEFAULT_MODEL,
        'messages': [
            {'role': 'user', 'content': '你好，这是一个测试请求'}
        ],
        'max_tokens': 50
    }
    
    try:
        response = requests.post(base_url, json=payload, headers=headers, timeout=10)
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ SiliconFlow API连接成功")
            data = response.json()
            if 'choices' in data and len(data['choices']) > 0:
                content = data['choices'][0]['message']['content']
                print(f"API响应内容: {content}")
                return True
            else:
                print("❌ API响应格式异常")
                return False
        else:
            print(f"❌ API调用失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ API调用异常: {e}")
        return False

def test_django_llm_function():
    """测试Django中的LLM函数"""
    print("\n🔧 测试Django LLM函数...")
    
    # 模拟检测数据
    detection_data = {
        'total_detections': 3,
        'glioma_tumor_count': 1,
        'meningioma_tumor_count': 1,
        'pituitary_tumor_count': 1,
        'model_name': 'YOLOv8',
        'confidence_threshold': 0.5
    }
    
    prompt = "请分析这个脑肿瘤检测结果"
    
    try:
        # 构建分析上下文
        context = build_analysis_context(detection_data, prompt)
        print("✅ 分析上下文构建成功")
        
        # 调用LLM API
        result = call_llm_api('Qwen/Qwen2.5-7B-Instruct', context)
        
        if result['success']:
            print("✅ Django LLM函数调用成功")
            print(f"分析结果: {result['content'][:100]}...")
            return True
        else:
            print(f"❌ Django LLM函数调用失败: {result['error']}")
            return False
            
    except Exception as e:
        print(f"❌ Django LLM函数异常: {e}")
        return False

def test_available_models():
    """测试可用模型"""
    print("\n📋 测试可用模型...")
    
    for model in AVAILABLE_MODELS:
        print(f"模型: {model['value']} - {model['name']}")
        
        try:
            # 测试每个模型
            payload = {
                'model': model['value'],
                'messages': [{'role': 'user', 'content': '测试'}],
                'max_tokens': 10
            }
            
            headers = {
                'Authorization': f'Bearer {SILICONFLOW_CONFIG["api_key"]}',
                'Content-Type': 'application/json'
            }
            
            response = requests.post(
                SILICONFLOW_CONFIG['base_url'], 
                json=payload, 
                headers=headers, 
                timeout=5
            )
            
            if response.status_code == 200:
                print(f"  ✅ {model['name']} 可用")
            else:
                print(f"  ❌ {model['name']} 不可用: {response.status_code}")
                
        except Exception as e:
            print(f"  ❌ {model['name']} 测试失败: {e}")

def check_django_settings():
    """检查Django设置"""
    print("\n⚙️  检查Django设置...")
    
    from django.conf import settings
    
    # 检查CORS设置
    if hasattr(settings, 'CORS_ALLOW_ALL_ORIGINS'):
        print(f"✅ CORS_ALLOW_ALL_ORIGINS: {settings.CORS_ALLOW_ALL_ORIGINS}")
    
    # 检查登录设置
    if hasattr(settings, 'LOGIN_URL'):
        print(f"✅ LOGIN_URL: {settings.LOGIN_URL}")
    
    # 检查中间件
    middleware = getattr(settings, 'MIDDLEWARE', [])
    auth_middleware = 'django.contrib.auth.middleware.AuthenticationMiddleware'
    if auth_middleware in middleware:
        print("✅ 认证中间件已配置")
    else:
        print("❌ 认证中间件未配置")

def main():
    """主函数"""
    print("🔍 LLM API 诊断工具")
    print("=" * 50)
    
    # 运行所有测试
    tests = [
        test_network_connection,
        test_siliconflow_api,
        test_django_llm_function,
        test_available_models,
        check_django_settings
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            results.append(False)
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 诊断总结:")
    passed = sum(1 for r in results if r)
    total = len(results)
    print(f"通过测试: {passed}/{total}")
    
    if all(results):
        print("✅ 所有测试通过，LLM API应该正常工作")
    else:
        print("❌ 存在问题，请检查失败的测试项")
        
        # 提供解决建议
        print("\n💡 解决建议:")
        print("1. 检查网络连接是否正常")
        print("2. 确认API密钥是否有效")
        print("3. 检查用户是否已登录Django系统")
        print("4. 确认CSRF令牌是否正确")
        print("5. 查看Django日志获取详细错误信息")

if __name__ == '__main__':
    main()
