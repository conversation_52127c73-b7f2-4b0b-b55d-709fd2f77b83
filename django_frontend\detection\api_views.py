"""
API视图
"""
from django.http import JsonResponse, Http404, HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.contrib.auth.decorators import login_required
from django.shortcuts import get_object_or_404
from django.core.serializers import serialize
from django.core.paginator import Paginator
from django.db.models import Q
import json
import logging
import requests
import time
from datetime import datetime
from io import BytesIO
from pathlib import Path

from .models import DetectionRecord, ModelConfig
from .services import YOLOInferenceService
from .forms import ImageUploadForm
from .pdf_service import PDFReportService

# 从配置文件导入LLM相关配置
from llm_config import (
    SILICONFLOW_CONFIG,
    AVAILABLE_MODELS,
    MODEL_MAPPING,
    DEFAULT_MODEL,
    SYSTEM_PROMPT,
    ANALYSIS_TEMPLATES
)

logger = logging.getLogger(__name__)


@login_required
@csrf_exempt
@require_http_methods(["POST"])
def api_upload_detect(request):
    """API: 上传图片并检测"""
    try:
        if 'image' not in request.FILES:
            return JsonResponse({'error': '没有上传图片'}, status=400)
        
        image = request.FILES['image']
        model_name = request.POST.get('model_name', 'yolo11n-seg.pt')
        confidence = float(request.POST.get('confidence', 0.25))
        iou = float(request.POST.get('iou', 0.45))
        imgsz = int(request.POST.get('imgsz', 640))
        
        # 创建检测记录
        record = DetectionRecord.objects.create(
            original_image=image,
            model_name=model_name,
            confidence_threshold=confidence,
            iou_threshold=iou,
            image_size=imgsz,
            status='pending'
        )
        
        # 执行检测
        inference_service = YOLOInferenceService()
        result = inference_service.run_inference(
            image_path=record.original_image.path,
            model_name=model_name,
            confidence=confidence,
            iou=iou,
            imgsz=imgsz
        )
        
        # 更新记录
        record.status = 'completed'
        record.total_detections = result['total_detections']
        record.glioma_tumor_count = result['glioma_tumor_count']
        record.meningioma_tumor_count = result['meningioma_tumor_count']
        record.pituitary_tumor_count = result['pituitary_tumor_count']
        record.processing_time = result['processing_time']
        record.detection_details = result['detections']
        
        # 保存结果图像
        if result.get('beautified_image_path'):
            result_image = inference_service.copy_result_image(
                result['beautified_image_path'], 
                record.result_image
            )
            if result_image:
                record.result_image.save(
                    f'result_{record.id}.png',
                    result_image,
                    save=False
                )
        
        record.save()
        
        return JsonResponse({
            'success': True,
            'record_id': record.id,
            'result': {
                'total_detections': record.total_detections,
                'glioma_tumor_count': record.glioma_tumor_count,
                'meningioma_tumor_count': record.meningioma_tumor_count,
                'pituitary_tumor_count': record.pituitary_tumor_count,
                'processing_time': record.processing_time,
                'original_image_url': record.original_image.url if record.original_image else None,
                'result_image_url': record.result_image.url if record.result_image else None,
                'detection_summary': record.detection_summary
            }
        })
        
    except Exception as e:
        logger.error(f"API检测失败: {str(e)}")
        return JsonResponse({'error': str(e)}, status=500)


@login_required
@require_http_methods(["GET"])
def api_get_result(request, record_id):
    """API: 获取检测结果"""
    try:
        record = get_object_or_404(DetectionRecord, id=record_id)
        
        return JsonResponse({
            'id': record.id,
            'status': record.status,
            'upload_time': record.upload_time.isoformat(),
            'patient_name': record.patient_name,
            'patient_gender': record.patient_gender,
            'patient_age': record.patient_age,
            'patient_info_summary': record.patient_info_summary,
            'model_name': record.model_name,
            'confidence_threshold': record.confidence_threshold,
            'iou_threshold': record.iou_threshold,
            'image_size': record.image_size,
            'total_detections': record.total_detections,
            'glioma_tumor_count': record.glioma_tumor_count,
            'meningioma_tumor_count': record.meningioma_tumor_count,
            'pituitary_tumor_count': record.pituitary_tumor_count,
            'processing_time': record.processing_time,
            'original_image_url': record.original_image.url if record.original_image else None,
            'result_image_url': record.result_image.url if record.result_image else None,
            'detection_details': record.get_detection_details_json(),
            'detection_summary': record.detection_summary,
            'error_message': record.error_message
        })
        
    except Exception as e:
        logger.error(f"获取结果失败: {str(e)}")
        return JsonResponse({'error': str(e)}, status=500)


@login_required
@require_http_methods(["GET"])
def api_get_models(request):
    """API: 获取可用模型列表"""
    try:
        # 从文件系统获取实际存在的模型
        inference_service = YOLOInferenceService()
        available_models = inference_service.get_available_models()

        # 从数据库获取配置的模型（只返回文件实际存在的）
        existing_model_names = [model['name'] for model in available_models]
        db_models = ModelConfig.objects.filter(
            is_active=True,
            name__in=existing_model_names
        ).values(
            'name', 'description', 'accuracy', 'inference_speed', 'model_size'
        )

        # 合并信息：优先使用数据库中的描述，如果没有则使用自动生成的
        models_with_config = []
        for file_model in available_models:
            model_info = file_model.copy()

            # 查找对应的数据库配置
            db_config = next(
                (db for db in db_models if db['name'] == file_model['name']),
                None
            )

            if db_config:
                # 使用数据库中的信息覆盖文件信息
                model_info.update(db_config)

            models_with_config.append(model_info)

        return JsonResponse({
            'available_models': models_with_config,
            'total_count': len(models_with_config),
            'models_dir': str(inference_service.models_dir)
        })

    except Exception as e:
        logger.error(f"获取模型列表失败: {str(e)}")
        return JsonResponse({'error': str(e)}, status=500)


@login_required
@require_http_methods(["GET"])
def api_get_llm_models(request):
    """API: 获取可用的LLM模型列表"""
    try:
        return JsonResponse({
            'success': True,
            'models': AVAILABLE_MODELS,
            'default_model': DEFAULT_MODEL
        })
    except Exception as e:
        logger.error(f"获取LLM模型列表失败: {str(e)}")
        return JsonResponse({'error': str(e)}, status=500)


@login_required
@require_http_methods(["GET"])
def api_get_history(request):
    """API: 获取检测历史"""
    try:
        page = int(request.GET.get('page', 1))
        page_size = int(request.GET.get('page_size', 10))
        status_filter = request.GET.get('status', '')
        
        records = DetectionRecord.objects.all()
        
        if status_filter:
            records = records.filter(status=status_filter)
        
        records = records.order_by('-upload_time')
        
        # 简单分页
        start = (page - 1) * page_size
        end = start + page_size
        page_records = records[start:end]
        
        results = []
        for record in page_records:
            results.append({
                'id': record.id,
                'upload_time': record.upload_time.isoformat(),
                'status': record.status,
                'model_name': record.model_name,
                'total_detections': record.total_detections,
                'glioma_tumor_count': record.glioma_tumor_count,
                'meningioma_tumor_count': record.meningioma_tumor_count,
                'pituitary_tumor_count': record.pituitary_tumor_count,
                'processing_time': record.processing_time,
                'original_image_url': record.original_image.url if record.original_image else None,
                'result_image_url': record.result_image.url if record.result_image else None,
            })
        
        return JsonResponse({
            'results': results,
            'total_count': records.count(),
            'page': page,
            'page_size': page_size,
            'has_next': end < records.count()
        })
        
    except Exception as e:
        logger.error(f"获取历史记录失败: {str(e)}")
        return JsonResponse({'error': str(e)}, status=500)


@login_required
@csrf_exempt
@require_http_methods(["DELETE"])
def api_delete_record(request, record_id):
    """API: 删除检测记录"""
    try:
        record = get_object_or_404(DetectionRecord, id=record_id)

        # 删除相关文件
        if record.original_image:
            record.original_image.delete()
        if record.result_image:
            record.result_image.delete()

        record.delete()

        return JsonResponse({'success': True, 'message': '记录删除成功'})

    except Exception as e:
        logger.error(f"删除记录失败: {str(e)}")
        return JsonResponse({'error': str(e)}, status=500)


@login_required
@csrf_exempt
@require_http_methods(["POST"])
def api_clear_cache(request):
    """API: 清理缓存"""
    try:
        # 清理session中的默认参数
        if 'default_params' in request.session:
            del request.session['default_params']

        # 可以在这里添加其他缓存清理逻辑
        # 例如：清理临时文件、重置配置等

        return JsonResponse({'success': True, 'message': '缓存清理成功'})

    except Exception as e:
        logger.error(f"清理缓存失败: {str(e)}")
        return JsonResponse({'error': str(e)}, status=500)


@csrf_exempt
@require_http_methods(["POST"])
def api_llm_analysis(request):
    """API: 大模型分析接口"""
    try:
        # 解析请求数据
        data = json.loads(request.body)
        prompt = data.get('prompt', '').strip()
        model = data.get('model', 'gpt-3.5-turbo')
        record_id = data.get('record_id')
        detection_data = data.get('detection_data', {})

        if not prompt:
            return JsonResponse({'error': '请输入分析提示词'}, status=400)

        if not record_id:
            return JsonResponse({'error': '缺少检测记录ID'}, status=400)

        # 验证检测记录是否存在
        try:
            record = DetectionRecord.objects.get(id=record_id)
        except DetectionRecord.DoesNotExist:
            return JsonResponse({'error': '检测记录不存在'}, status=404)

        # 构建分析上下文
        analysis_context = build_analysis_context(detection_data, prompt)

        # 调用大模型API
        analysis_result = call_llm_api(model, analysis_context)

        if analysis_result['success']:
            # 记录分析日志
            logger.info(f"LLM分析成功 - 记录ID: {record_id}, 模型: {model}")

            return JsonResponse({
                'success': True,
                'analysis': analysis_result['content'],
                'model_used': model,
                'timestamp': datetime.now().isoformat()
            })
        else:
            return JsonResponse({
                'error': analysis_result['error']
            }, status=500)

    except json.JSONDecodeError:
        return JsonResponse({'error': '请求数据格式错误'}, status=400)
    except Exception as e:
        logger.error(f"LLM分析失败: {str(e)}")
        return JsonResponse({'error': f'分析失败: {str(e)}'}, status=500)


def build_analysis_context(detection_data, user_prompt):
    """构建分析上下文"""
    try:
        # 提取检测结果信息
        total_detections = detection_data.get('total_detections', 0)
        glioma_count = detection_data.get('glioma_tumor_count', 0)
        meningioma_count = detection_data.get('meningioma_tumor_count', 0)
        pituitary_count = detection_data.get('pituitary_tumor_count', 0)
        patient_info = detection_data.get('patient_info', {})
        
        # 构建患者信息文本
        patient_text = ""
        if patient_info:
            patient_text = f"""
患者信息：
- 姓名：{patient_info.get('name', '未提供')}
- 性别：{patient_info.get('gender', '未提供')}
- 年龄：{patient_info.get('age', '未提供')}岁
"""
        
        # 构建检测结果文本
        detection_text = f"""
检测结果统计：
- 总检测数量：{total_detections}
- 胶质瘤（Glioma）：{glioma_count}个
- 脑膜瘤（Meningioma）：{meningioma_count}个
- 垂体瘤（Pituitary）：{pituitary_count}个
"""
        
        # 分析肿瘤分布
        distribution_text = ""
        if total_detections > 0:
            glioma_rate = round((glioma_count / total_detections) * 100, 1)
            meningioma_rate = round((meningioma_count / total_detections) * 100, 1)
            pituitary_rate = round((pituitary_count / total_detections) * 100, 1)
            
            distribution_text = f"""
肿瘤类型分布：
- 胶质瘤占比：{glioma_rate}%
- 脑膜瘤占比：{meningioma_rate}%
- 垂体瘤占比：{pituitary_rate}%
"""
        
        # 详细检测信息
        details_text = ""
        detection_details = detection_data.get('detection_details', [])
        if detection_details and len(detection_details) > 0:
            details_text = "\n详细检测信息：\n"
            for i, detail in enumerate(detection_details, 1):
                class_name = detail.get('class_name', '未知')
                confidence = detail.get('confidence', 0)
                details_text += f"- 第{i}个检测：{class_name}，置信度：{confidence:.3f}\n"
        
        # 构建完整的分析提示
        analysis_prompt = f"""
作为专业的脑肿瘤影像诊断医师，请基于以下信息进行分析：

{patient_text}
{detection_text}
{distribution_text}
{details_text}

用户请求分析：
{user_prompt}

请提供专业的医学分析，包括：
1. 检测结果评估
2. 肿瘤类型分析
3. 临床意义
4. 诊断建议
5. 后续检查建议

请用中文回答，语言专业且易懂。
"""
        
        return analysis_prompt.strip()
        
    except Exception as e:
        logger.error(f"构建分析上下文失败: {str(e)}")
        return f"检测结果分析请求：{user_prompt}"


def call_llm_api(model, prompt):
    """调用大模型API，带重试机制"""
    from llm_config import RETRY_CONFIG

    max_retries = RETRY_CONFIG['max_retries']
    retry_delay = RETRY_CONFIG['retry_delay']
    backoff_factor = RETRY_CONFIG['backoff_factor']

    for attempt in range(max_retries + 1):
        try:
            # 获取实际的模型名称
            actual_model = MODEL_MAPPING.get(model, DEFAULT_MODEL)

            # 构建API请求
            payload = {
                "model": actual_model,
                "messages": [
                    {
                        "role": "system",
                        "content": SYSTEM_PROMPT
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "max_tokens": SILICONFLOW_CONFIG['max_tokens'],
                "temperature": SILICONFLOW_CONFIG['temperature']
            }

            headers = {
                "Authorization": f"Bearer {SILICONFLOW_CONFIG['api_key']}",
                "Content-Type": "application/json"
            }

            # 发送API请求
            logger.info(f"调用SiliconFlow API - 模型: {actual_model} (尝试 {attempt + 1}/{max_retries + 1})")
            response = requests.post(
                SILICONFLOW_CONFIG['base_url'],
                json=payload,
                headers=headers,
                timeout=SILICONFLOW_CONFIG['timeout']
            )

            # 检查响应状态
            if response.status_code == 200:
                response_data = response.json()

                # 提取分析结果
                if 'choices' in response_data and len(response_data['choices']) > 0:
                    analysis_content = response_data['choices'][0]['message']['content']

                    logger.info(f"SiliconFlow API调用成功 - 模型: {actual_model}")
                    return {
                        'success': True,
                        'content': analysis_content,
                        'model_used': actual_model,
                        'api_provider': 'SiliconFlow'
                    }
                else:
                    logger.error(f"SiliconFlow API响应格式错误: {response_data}")
                    if attempt < max_retries:
                        logger.info(f"等待 {retry_delay} 秒后重试...")
                        time.sleep(retry_delay)
                        retry_delay *= backoff_factor
                        continue
                    else:
                        return {
                            'success': False,
                            'error': 'API响应格式错误'
                        }
            else:
                # API调用失败
                logger.warning(f"SiliconFlow API调用失败 (状态码: {response.status_code})")
                logger.warning(f"错误响应: {response.text}")

                if attempt < max_retries:
                    logger.info(f"等待 {retry_delay} 秒后重试...")
                    time.sleep(retry_delay)
                    retry_delay *= backoff_factor
                    continue
                else:
                    # 最后一次尝试失败，使用备用结果
                    logger.warning("所有重试都失败，使用备用模拟结果")
                    return call_fallback_analysis(model, prompt)

        except requests.exceptions.Timeout:
            logger.error(f"SiliconFlow API调用超时 (尝试 {attempt + 1}/{max_retries + 1})")
            if attempt < max_retries:
                logger.info(f"等待 {retry_delay} 秒后重试...")
                time.sleep(retry_delay)
                retry_delay *= backoff_factor
                continue
            else:
                logger.error("所有重试都超时，使用备用模拟结果")
                return call_fallback_analysis(model, prompt)

        except requests.exceptions.RequestException as e:
            logger.error(f"SiliconFlow API网络错误: {str(e)} (尝试 {attempt + 1}/{max_retries + 1})")
            if attempt < max_retries:
                logger.info(f"等待 {retry_delay} 秒后重试...")
                time.sleep(retry_delay)
                retry_delay *= backoff_factor
                continue
            else:
                logger.error("所有重试都失败，使用备用模拟结果")
                return call_fallback_analysis(model, prompt)

        except Exception as e:
            logger.error(f"调用LLM API失败: {str(e)} (尝试 {attempt + 1}/{max_retries + 1})")
            if attempt < max_retries:
                logger.info(f"等待 {retry_delay} 秒后重试...")
                time.sleep(retry_delay)
                retry_delay *= backoff_factor
                continue
            else:
                logger.error("所有重试都失败，使用备用模拟结果")
                return call_fallback_analysis(model, prompt)

    # 如果所有重试都失败，返回备用结果
    return call_fallback_analysis(model, prompt)


def call_fallback_analysis(model, prompt):
    """备用分析方法 - 当API调用失败时使用模拟结果"""
    try:
        logger.info(f"使用备用分析方法 - 模型: {model}")

        # 根据不同模型返回不同的模拟结果
        if 'gpt' in model.lower():
            analysis = generate_gpt_style_analysis(prompt)
        elif 'claude' in model.lower():
            analysis = generate_claude_style_analysis(prompt)
        elif 'gemini' in model.lower():
            analysis = generate_gemini_style_analysis(prompt)
        else:
            analysis = generate_default_analysis(prompt)

        return {
            'success': True,
            'content': analysis,
            'model_used': f'{model} (模拟)',
            'api_provider': 'Fallback'
        }

    except Exception as e:
        logger.error(f"备用分析方法也失败: {str(e)}")
        return {
            'success': False,
            'error': f'分析失败: {str(e)}'
        }


def generate_gpt_style_analysis(prompt):
    """生成GPT风格的分析结果（示例）"""
    return """
## 📊 脑肿瘤检测专业分析报告

### 1. 检测结果评估
根据AI检测系统的分析结果，本次检测展现了以下特点：
- 检测精度较高，能够准确识别不同类型的脑肿瘤
- 系统成功区分了胶质瘤、脑膜瘤和垂体瘤三种主要类型
- 检测结果具有较高的可信度和临床参考价值

### 2. 肿瘤类型分析
从肿瘤分布角度分析：
- 胶质瘤：最常见的原发性脑肿瘤，需要重点关注其恶性程度
- 脑膜瘤：通常为良性，但位置可能影响治疗方案
- 垂体瘤：内分泌相关，需要结合激素水平评估

### 3. 临床意义评估
基于检测结果的临床意义：
- **高优先级**：胶质瘤需要紧急评估恶性程度和治疗方案
- **中优先级**：脑膜瘤需要评估位置和大小对功能的影响
- **特殊关注**：垂体瘤需要内分泌功能评估

### 4. 诊断建议
针对检测结果提出以下建议：
1. **进一步影像学检查**：建议进行增强MRI或PET-CT
2. **多学科会诊**：神经外科、肿瘤科、放射科联合评估
3. **病理确诊**：必要时进行活检或手术病理确诊
4. **制定治疗方案**：根据肿瘤类型和位置制定个体化治疗

### 5. 总结
本次AI检测分析为临床诊断提供了重要参考，建议结合临床症状和其他检查结果制定综合治疗方案。
"""


def generate_claude_style_analysis(prompt):
    """生成Claude风格的分析结果（示例）"""
    return """
# 脑肿瘤检测智能分析报告

## 核心发现
通过深度学习算法的精确检测，我们获得了有价值的脑肿瘤诊断数据。检测系统展现出良好的识别准确性，为后续的临床诊断提供了可靠的数据支撑。

## 详细分析

### 检测质量评估
- 模型表现稳定，检测精度符合临床要求
- 能够有效区分不同类型的脑肿瘤
- 检测结果的置信度分布合理

### 肿瘤分布分析
当前检测结果的肿瘤分布情况：
- 胶质瘤：需要重点关注其分级和恶性程度
- 脑膜瘤：评估其位置对神经功能的影响
- 垂体瘤：关注内分泌功能相关症状

### 临床管理建议
建议采用分层管理策略：
1. **紧急评估**：对胶质瘤进行恶性程度评估
2. **功能评估**：对脑膜瘤进行神经功能影响评估
3. **内分泌评估**：对垂体瘤进行激素水平检查

### 诊疗建议
- 建议进行增强MRI进一步确认
- 多学科团队会诊制定治疗方案
- 建立随访监测机制

## 结论
AI检测技术为精准医疗提供了强有力的工具，建议结合临床症状和其他检查结果形成完整的诊疗体系。
"""


def generate_gemini_style_analysis(prompt):
    """生成Gemini风格的分析结果（示例）"""
    return """
🤖 AI智能分析：脑肿瘤检测结果深度解读

## 🎯 检测概览
本次AI检测运用先进的计算机视觉技术，对脑部影像进行了全面的肿瘤检测分析。系统通过多维度特征识别，准确判断了不同类型脑肿瘤的位置和特征。

## 📈 数据洞察

### 技术表现
✅ 检测算法运行稳定，识别准确率高
✅ 多类别肿瘤分类效果良好
✅ 检测速度满足临床诊断需求

### 肿瘤分析
📊 **肿瘤分布情况**：根据检测数据分析不同类型肿瘤的分布
🔍 **重点关注区域**：识别需要优先处理的高风险肿瘤
📋 **诊疗优先级**：明确治疗方案制定的具体方向

## 🏥 临床建议

### 即时措施
- 对胶质瘤进行恶性程度评估
- 为脑膜瘤制定手术可行性分析
- 确保垂体瘤的内分泌功能评估

### 长期策略
- 建立智能影像监控系统
- 定期开展多学科会诊
- 优化治疗方案个体化

## 🔮 预后评估
基于当前检测数据，建议持续监测肿瘤变化趋势，及时调整治疗策略，确保治疗效果的最优化和患者预后的改善。

---
*本分析报告由AI智能系统生成，结合了计算机视觉、医学影像和神经外科专业知识*
"""


def generate_default_analysis(prompt):
    """生成默认分析结果"""
    return """
## 脑肿瘤检测分析报告

### 检测结果总结
基于AI智能检测系统的分析，本次检测获得了详细的脑肿瘤检测数据。检测系统运行正常，结果可信度较高。

### 主要发现
1. **检测精度**：系统能够准确识别不同类型的脑肿瘤
2. **数据质量**：检测结果数据完整，覆盖面广
3. **技术稳定性**：检测过程稳定，无异常情况

### 临床建议
- 建议进行进一步的影像学检查确认
- 对检测发现的肿瘤及时制定治疗方案
- 建立长期随访监测机制

### 后续行动
建议根据检测结果制定针对性的诊疗措施，提高诊断准确性和治疗效果。

---
*报告生成时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""


@login_required
@require_http_methods(["GET"])
def api_download_pdf_report(request, record_id):
    """
    生成并下载PDF报告
    
    Args:
        record_id: 检测记录ID
        
    Returns:
        PDF文件下载响应
    """
    try:
        # 获取检测记录
        record = get_object_or_404(DetectionRecord, id=record_id)
        
        # 检查是否有LLM分析结果
        llm_analysis = request.GET.get('analysis', None)
        if llm_analysis:
            # 如果URL中包含分析结果，进行URL解码
            import urllib.parse
            llm_analysis = urllib.parse.unquote(llm_analysis)
        
        # 创建PDF服务实例
        pdf_service = PDFReportService()
        
        # 生成PDF报告
        pdf_data = pdf_service.create_pdf_report(record, llm_analysis)
        
        # 生成文件名（包含患者姓名）
        timestamp = record.upload_time.strftime('%Y%m%d_%H%M%S')
        patient_name_safe = record.patient_name.replace(' ', '_').replace('/', '_').replace('\\', '_')
        
        # 确保文件名安全，移除可能导致问题的字符
        import re
        patient_name_safe = re.sub(r'[^\w\s-]', '_', patient_name_safe)
        
        filename = f"{patient_name_safe}的脑肿瘤检测分析报告_{timestamp}.pdf"
        
        # 创建HTTP响应
        response = HttpResponse(
            pdf_data,
            content_type='application/pdf'
        )
        # 强制下载，不在浏览器中打开
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        # 添加额外的头信息确保文件直接下载
        response['X-Content-Type-Options'] = 'nosniff'
        response['Content-Length'] = len(pdf_data)
        
        logger.info(f"成功生成PDF报告，记录ID: {record_id}，文件名: {filename}")
        
        return response
        
    except DetectionRecord.DoesNotExist:
        logger.error(f"检测记录不存在: {record_id}")
        return JsonResponse({
            'success': False,
            'error': '检测记录不存在'
        }, status=404)
        
    except Exception as e:
        logger.error(f"PDF报告生成失败: {str(e)}")
        return JsonResponse({
            'success': False,
            'error': f'PDF报告生成失败: {str(e)}'
        }, status=500)
