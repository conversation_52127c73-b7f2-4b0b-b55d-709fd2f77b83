"""
表单定义
"""
from django import forms
from django.core.exceptions import ValidationError
from django.conf import settings
import os
from .models import DetectionRecord, ModelConfig


class ImageUploadForm(forms.ModelForm):
    """图片上传表单"""
    
    class Meta:
        model = DetectionRecord
        fields = [
            'patient_name',
            'patient_gender', 
            'patient_age',
            'original_image', 
            'model_name', 
            'confidence_threshold', 
            'iou_threshold', 
            'image_size'
        ]
        widgets = {
            'patient_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '请输入患者姓名',
                'required': True
            }),
            'patient_gender': forms.Select(attrs={
                'class': 'form-select',
                'required': True
            }),
            'patient_age': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': '请输入患者年龄',
                'min': '0',
                'max': '200',
                'required': True
            }),
            'original_image': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': 'image/*',
                'id': 'imageInput'
            }),
            'model_name': forms.Select(attrs={
                'class': 'form-select'
            }),
            'confidence_threshold': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '0.1',
                'max': '1.0',
                'step': '0.05'
            }),
            'iou_threshold': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '0.1',
                'max': '1.0',
                'step': '0.05'
            }),
            'image_size': forms.Select(attrs={
                'class': 'form-select'
            }, choices=[
                (320, '320x320 (快速)'),
                (640, '640x640 (标准)'),
                (1280, '1280x1280 (高精度)'),
            ])
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # 动态获取实际存在的模型文件
        model_choices = self._get_available_models()
        self.fields['model_name'].widget.choices = model_choices
        
        # 设置字段标签
        self.fields['patient_name'].label = '患者姓名'
        self.fields['patient_gender'].label = '患者性别'
        self.fields['patient_age'].label = '患者年龄'
        self.fields['original_image'].label = '选择图片'
        self.fields['model_name'].label = '检测模型'
        self.fields['confidence_threshold'].label = '置信度阈值'
        self.fields['iou_threshold'].label = 'IOU阈值'
        self.fields['image_size'].label = '图像尺寸'
        
        # 设置帮助文本
        self.fields['patient_name'].help_text = '医学影像对应的患者姓名'
        self.fields['patient_age'].help_text = '患者年龄，用于AI分析参考'
        self.fields['confidence_threshold'].help_text = '检测置信度阈值，越高越严格'
        self.fields['iou_threshold'].help_text = '重叠度阈值，用于去除重复检测'
        self.fields['image_size'].help_text = '输入图像尺寸，越大精度越高但速度越慢'

    def _get_available_models(self):
        """获取实际存在的模型文件"""
        from django.conf import settings
        from pathlib import Path

        model_choices = []
        models_dir = settings.YOLO_MODELS_DIR

        if models_dir.exists():
            # 扫描checkpoints目录下的.pt文件
            for model_file in models_dir.glob('*.pt'):
                model_name = model_file.name

                # 尝试从数据库获取模型描述
                try:
                    from .models import ModelConfig
                    model_config = ModelConfig.objects.filter(
                        name=model_name,
                        is_active=True
                    ).first()

                    if model_config:
                        description = model_config.description
                    else:
                        # 根据文件名生成描述
                        description = self._generate_model_description(model_name)

                    model_choices.append((model_name, f"{model_name} - {description}"))

                except Exception:
                    # 如果数据库查询失败，使用默认描述
                    description = self._generate_model_description(model_name)
                    model_choices.append((model_name, f"{model_name} - {description}"))

        # 如果没有找到任何模型文件，提供默认选项
        if not model_choices:
            model_choices = [
                ('yolo11n-seg.pt', 'YOLO11n-seg.pt - 默认模型（请确保文件存在）'),
            ]

        # 按文件名排序
        model_choices.sort(key=lambda x: x[0])
        return model_choices

    def _generate_model_description(self, model_name):
        """根据模型文件名生成描述"""
        name_lower = model_name.lower()

        if 'yolo11n' in name_lower:
            return 'YOLO11 Nano - 快速检测'
        elif 'yolo11s' in name_lower:
            return 'YOLO11 Small - 平衡性能'
        elif 'yolo11m' in name_lower:
            return 'YOLO11 Medium - 高精度'
        elif 'yolo11l' in name_lower:
            return 'YOLO11 Large - 超高精度'
        elif 'yolo11x' in name_lower:
            return 'YOLO11 XLarge - 最高精度'
        elif 'seg' in name_lower:
            return '分割模型'
        elif 'det' in name_lower:
            return '检测模型'
        else:
            return '自定义模型'
    
    def clean_original_image(self):
        """验证上传的图片"""
        image = self.cleaned_data.get('original_image')
        
        if not image:
            raise ValidationError('请选择要上传的图片')
        
        # 检查文件大小
        if image.size > settings.MAX_IMAGE_SIZE:
            raise ValidationError(
                f'图片文件过大，最大支持 {settings.MAX_IMAGE_SIZE // (1024*1024)}MB'
            )
        
        # 检查文件扩展名
        ext = os.path.splitext(image.name)[1].lower()
        if ext not in settings.ALLOWED_IMAGE_EXTENSIONS:
            raise ValidationError(
                f'不支持的图片格式，支持的格式: {", ".join(settings.ALLOWED_IMAGE_EXTENSIONS)}'
            )
        
        return image
    
    def clean_confidence_threshold(self):
        """验证置信度阈值"""
        confidence = self.cleaned_data.get('confidence_threshold')
        if confidence is not None and (confidence < 0.1 or confidence > 1.0):
            raise ValidationError('置信度阈值必须在0.1到1.0之间')
        return confidence
    
    def clean_iou_threshold(self):
        """验证IOU阈值"""
        iou = self.cleaned_data.get('iou_threshold')
        if iou is not None and (iou < 0.1 or iou > 1.0):
            raise ValidationError('IOU阈值必须在0.1到1.0之间')
        return iou

    def clean_model_name(self):
        """验证模型文件是否存在"""
        model_name = self.cleaned_data.get('model_name')
        if model_name:
            # 检查模型文件是否实际存在
            from django.conf import settings
            model_path = settings.YOLO_MODELS_DIR / model_name
            if not model_path.exists():
                raise ValidationError(f'模型文件不存在: {model_name}')
        return model_name
    
    def clean_patient_name(self):
        """验证患者姓名"""
        name = self.cleaned_data.get('patient_name')
        if not name:
            raise ValidationError('请输入患者姓名')
        if len(name.strip()) < 2:
            raise ValidationError('患者姓名至少需要2个字符')
        if len(name.strip()) > 50:
            raise ValidationError('患者姓名不能超过50个字符')
        return name.strip()
    
    def clean_patient_age(self):
        """验证患者年龄"""
        age = self.cleaned_data.get('patient_age')
        if age is None:
            raise ValidationError('请输入患者年龄')
        if age < 0 or age > 200:
            raise ValidationError('患者年龄必须在0到200岁之间')
        return age
    
    def clean_patient_gender(self):
        """验证患者性别"""
        gender = self.cleaned_data.get('patient_gender')
        valid_choices = ['male', 'female', 'other']
        if gender not in valid_choices:
            raise ValidationError('请选择有效的性别选项')
        return gender


class DetectionParametersForm(forms.Form):
    """检测参数调整表单"""
    
    model_name = forms.ChoiceField(
        label='检测模型',
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    confidence_threshold = forms.FloatField(
        label='置信度阈值',
        initial=0.25,
        min_value=0.1,
        max_value=1.0,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'step': '0.05'
        })
    )
    iou_threshold = forms.FloatField(
        label='IOU阈值',
        initial=0.45,
        min_value=0.1,
        max_value=1.0,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'step': '0.05'
        })
    )
    image_size = forms.ChoiceField(
        label='图像尺寸',
        choices=[
            (320, '320x320 (快速)'),
            (640, '640x640 (标准)'),
            (1280, '1280x1280 (高精度)'),
        ],
        initial=640,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # 动态获取实际存在的模型文件
        model_choices = self._get_available_models()
        self.fields['model_name'].choices = model_choices

    def _get_available_models(self):
        """获取实际存在的模型文件"""
        from django.conf import settings
        from pathlib import Path

        model_choices = []
        models_dir = settings.YOLO_MODELS_DIR

        if models_dir.exists():
            # 扫描checkpoints目录下的.pt文件
            for model_file in models_dir.glob('*.pt'):
                model_name = model_file.name
                model_choices.append((model_name, model_name))

        # 如果没有找到任何模型文件，提供默认选项
        if not model_choices:
            model_choices = [
                ('yolo11n-seg.pt', 'yolo11n-seg.pt（请确保文件存在）'),
            ]

        # 按文件名排序
        model_choices.sort(key=lambda x: x[0])
        return model_choices
