from django.core.management.base import BaseCommand
from detection.models import DetectionRecord
from detection.services import YOLOInferenceService
from pathlib import Path

class Command(BaseCommand):
    help = 'Update detection data by re-parsing label files'

    def handle(self, *args, **options):
        try:
            # Get latest record
            record = DetectionRecord.objects.order_by('-id').first()
            if not record:
                self.stdout.write(self.style.ERROR('No detection records found'))
                return

            self.stdout.write(f'Updating record ID: {record.id}, Patient: {record.patient_name}')

            # Find label files
            media_dir = Path("media/temp_inference")
            labels_dirs = list(media_dir.glob("exp*/labels"))
            
            if not labels_dirs:
                self.stdout.write(self.style.ERROR('No labels directories found'))
                return

            latest_labels_dir = max(labels_dirs, key=lambda x: x.stat().st_mtime)
            self.stdout.write(f'Using labels directory: {latest_labels_dir}')

            # Parse label files
            inference_service = YOLOInferenceService()
            label_files = list(latest_labels_dir.glob("*.txt"))
            all_detections = []
            
            for label_file in label_files:
                detections = inference_service._parse_label_file(label_file)
                all_detections.extend(detections)
                self.stdout.write(f'Parsed {label_file.name}: {len(detections)} detections')

            if not all_detections:
                self.stdout.write(self.style.ERROR('No detections found'))
                return

            # Update record
            record.detection_details = all_detections
            record.total_detections = len(all_detections)
            record.glioma_tumor_count = sum(1 for d in all_detections if d['class_id'] == 0)
            record.meningioma_tumor_count = sum(1 for d in all_detections if d['class_id'] == 1)
            record.pituitary_tumor_count = sum(1 for d in all_detections if d['class_id'] == 2)
            record.save()

            self.stdout.write(self.style.SUCCESS(f'Record updated successfully:'))
            self.stdout.write(f'  Total detections: {record.total_detections}')
            self.stdout.write(f'  Glioma: {record.glioma_tumor_count}')
            self.stdout.write(f'  Meningioma: {record.meningioma_tumor_count}')
            self.stdout.write(f'  Pituitary: {record.pituitary_tumor_count}')

            # Show first detection details
            if all_detections:
                first = all_detections[0]
                self.stdout.write(f'\nFirst detection details:')
                self.stdout.write(f'  Class: {first["class_name"]}')
                self.stdout.write(f'  Center: ({first["x_center"]:.4f}, {first["y_center"]:.4f})')
                self.stdout.write(f'  Size: {first["width"]:.4f} x {first["height"]:.4f}')
                self.stdout.write(f'  Confidence: {first["confidence"]:.4f}')

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error: {str(e)}'))
            import traceback
            traceback.print_exc()
