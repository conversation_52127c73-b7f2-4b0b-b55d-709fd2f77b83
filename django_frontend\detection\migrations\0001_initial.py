# Generated by Django 5.2.3 on 2025-07-01 03:15

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="DetectionRecord",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "upload_time",
                    models.DateTimeField(auto_now_add=True, verbose_name="上传时间"),
                ),
                (
                    "original_image",
                    models.ImageField(
                        upload_to="uploads/original/%Y/%m/%d/", verbose_name="原始图片"
                    ),
                ),
                (
                    "result_image",
                    models.ImageField(
                        blank=True,
                        null=True,
                        upload_to="uploads/results/%Y/%m/%d/",
                        verbose_name="检测结果图片",
                    ),
                ),
                (
                    "model_name",
                    models.CharField(
                        default="yolo11n-seg.pt",
                        max_length=100,
                        verbose_name="使用的模型",
                    ),
                ),
                (
                    "confidence_threshold",
                    models.FloatField(default=0.25, verbose_name="置信度阈值"),
                ),
                (
                    "iou_threshold",
                    models.FloatField(default=0.45, verbose_name="IOU阈值"),
                ),
                (
                    "image_size",
                    models.IntegerField(default=640, verbose_name="图像尺寸"),
                ),
                (
                    "total_detections",
                    models.IntegerField(default=0, verbose_name="总检测数量"),
                ),
                (
                    "glioma_tumor_count",
                    models.IntegerField(default=0, verbose_name="胶质瘤数量"),
                ),
                (
                    "meningioma_tumor_count",
                    models.IntegerField(default=0, verbose_name="脑膜瘤数量"),
                ),
                (
                    "pituitary_tumor_count",
                    models.IntegerField(default=0, verbose_name="垂体瘤数量"),
                ),
                (
                    "detection_details",
                    models.JSONField(
                        default=dict,
                        help_text="包含每个检测框的坐标、置信度等详细信息",
                        verbose_name="检测详细结果",
                    ),
                ),
                (
                    "processing_time",
                    models.FloatField(
                        blank=True, null=True, verbose_name="处理时间(秒)"
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "等待处理"),
                            ("processing", "处理中"),
                            ("completed", "已完成"),
                            ("failed", "处理失败"),
                        ],
                        default="pending",
                        max_length=20,
                        verbose_name="处理状态",
                    ),
                ),
                (
                    "error_message",
                    models.TextField(blank=True, null=True, verbose_name="错误信息"),
                ),
            ],
            options={
                "verbose_name": "检测记录",
                "verbose_name_plural": "检测记录",
                "ordering": ["-upload_time"],
            },
        ),
        migrations.CreateModel(
            name="ModelConfig",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        max_length=100, unique=True, verbose_name="模型名称"
                    ),
                ),
                (
                    "file_path",
                    models.CharField(max_length=255, verbose_name="模型文件路径"),
                ),
                ("description", models.TextField(blank=True, verbose_name="模型描述")),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="是否启用"),
                ),
                (
                    "created_time",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "accuracy",
                    models.FloatField(blank=True, null=True, verbose_name="准确率"),
                ),
                (
                    "inference_speed",
                    models.FloatField(
                        blank=True, null=True, verbose_name="推理速度(ms)"
                    ),
                ),
                (
                    "model_size",
                    models.FloatField(
                        blank=True, null=True, verbose_name="模型大小(MB)"
                    ),
                ),
            ],
            options={
                "verbose_name": "模型配置",
                "verbose_name_plural": "模型配置",
                "ordering": ["-created_time"],
            },
        ),
    ]
