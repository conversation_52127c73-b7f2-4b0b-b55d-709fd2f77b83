# Generated by Django 5.2.3 on 2025-07-01 07:26

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("detection", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="LLMAnalysisRecord",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "user_prompt",
                    models.TextField(help_text="用户输入的问题或分析需求", verbose_name="用户提示词"),
                ),
                (
                    "llm_response",
                    models.TextField(help_text="大模型返回的分析结果", verbose_name="LLM响应"),
                ),
                (
                    "created_time",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "api_provider",
                    models.CharField(
                        default="openai",
                        help_text="使用的大模型API提供商",
                        max_length=50,
                        verbose_name="API提供商",
                    ),
                ),
                (
                    "model_name",
                    models.CharField(
                        default="gpt-3.5-turbo",
                        help_text="使用的具体模型名称",
                        max_length=100,
                        verbose_name="模型名称",
                    ),
                ),
                (
                    "response_time",
                    models.FloatField(
                        blank=True,
                        help_text="API调用的响应时间",
                        null=True,
                        verbose_name="响应时间(秒)",
                    ),
                ),
                (
                    "token_usage",
                    models.JSONField(
                        blank=True,
                        help_text="API调用的token消耗统计",
                        null=True,
                        verbose_name="Token使用量",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "处理中"),
                            ("completed", "已完成"),
                            ("failed", "失败"),
                        ],
                        default="pending",
                        max_length=20,
                        verbose_name="状态",
                    ),
                ),
                (
                    "error_message",
                    models.TextField(
                        blank=True, help_text="如果失败，记录错误详情", verbose_name="错误信息"
                    ),
                ),
                (
                    "detection_record",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="llm_analyses",
                        to="detection.detectionrecord",
                        verbose_name="检测记录",
                    ),
                ),
            ],
            options={
                "verbose_name": "LLM分析记录",
                "verbose_name_plural": "LLM分析记录",
                "ordering": ["-created_time"],
            },
        ),
    ]
