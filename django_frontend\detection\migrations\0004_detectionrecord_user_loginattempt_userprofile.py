# Generated by Django 5.2.3 on 2025-07-02 03:28

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("detection", "0003_delete_llmanalysisrecord"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="detectionrecord",
            name="user",
            field=models.ForeignKey(
                default=1,
                help_text="检测记录所属用户",
                on_delete=django.db.models.deletion.CASCADE,
                to=settings.AUTH_USER_MODEL,
                verbose_name="用户",
            ),
            preserve_default=False,
        ),
        migrations.CreateModel(
            name="LoginAttempt",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("username", models.<PERSON>r<PERSON><PERSON>(max_length=150, verbose_name="用户名")),
                ("ip_address", models.GenericIPAddressField(verbose_name="IP地址")),
                (
                    "user_agent",
                    models.CharField(
                        blank=True, max_length=500, verbose_name="用户代理"
                    ),
                ),
                (
                    "success",
                    models.BooleanField(default=False, verbose_name="是否成功"),
                ),
                (
                    "attempt_time",
                    models.DateTimeField(auto_now_add=True, verbose_name="尝试时间"),
                ),
                (
                    "failure_reason",
                    models.CharField(
                        blank=True, max_length=200, verbose_name="失败原因"
                    ),
                ),
            ],
            options={
                "verbose_name": "登录尝试",
                "verbose_name_plural": "登录尝试",
                "ordering": ["-attempt_time"],
                "indexes": [
                    models.Index(
                        fields=["username", "ip_address"],
                        name="detection_l_usernam_bf0399_idx",
                    ),
                    models.Index(
                        fields=["attempt_time"], name="detection_l_attempt_73380c_idx"
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="UserProfile",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "failed_login_attempts",
                    models.IntegerField(default=0, verbose_name="失败登录次数"),
                ),
                (
                    "account_locked_until",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="账户锁定至"
                    ),
                ),
                (
                    "last_login_ip",
                    models.GenericIPAddressField(
                        blank=True, null=True, verbose_name="最后登录IP"
                    ),
                ),
                (
                    "total_detections",
                    models.IntegerField(default=0, verbose_name="总检测次数"),
                ),
                (
                    "created_time",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_time",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="用户",
                    ),
                ),
            ],
            options={
                "verbose_name": "用户资料",
                "verbose_name_plural": "用户资料",
            },
        ),
    ]
