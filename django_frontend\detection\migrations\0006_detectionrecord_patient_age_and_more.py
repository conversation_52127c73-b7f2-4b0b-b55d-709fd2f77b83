# Generated by Django 5.2.3 on 2025-07-02 09:37

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("detection", "0005_delete_loginattempt_remove_detectionrecord_user_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="detectionrecord",
            name="patient_age",
            field=models.PositiveIntegerField(
                default=0, help_text="患者年龄（岁）", verbose_name="患者年龄"
            ),
        ),
        migrations.AddField(
            model_name="detectionrecord",
            name="patient_gender",
            field=models.CharField(
                choices=[("male", "男"), ("female", "女"), ("other", "其他")],
                default="other",
                max_length=10,
                verbose_name="患者性别",
            ),
        ),
        migrations.AddField(
            model_name="detectionrecord",
            name="patient_name",
            field=models.CharField(
                default="匿名患者",
                help_text="患者姓名",
                max_length=100,
                verbose_name="患者姓名",
            ),
        ),
    ]
