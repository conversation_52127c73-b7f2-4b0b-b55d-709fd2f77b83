"""
脑肿瘤检测应用的数据模型
"""
from django.db import models
from django.utils import timezone
import json


class DetectionRecord(models.Model):
    """检测记录模型"""
    
    # 患者信息
    patient_name = models.CharField(
        max_length=100,
        verbose_name="患者姓名",
        help_text="患者姓名",
        default="匿名患者"
    )
    patient_gender = models.CharField(
        max_length=10,
        choices=[
            ('male', '男'),
            ('female', '女'),
            ('other', '其他')
        ],
        verbose_name="患者性别",
        default='other'
    )
    patient_age = models.PositiveIntegerField(
        verbose_name="患者年龄",
        help_text="患者年龄（岁）",
        default=0
    )
    
    # 基本信息
    upload_time = models.DateTimeField(auto_now_add=True, verbose_name="上传时间")
    original_image = models.ImageField(
        upload_to='uploads/original/%Y/%m/%d/', 
        verbose_name="原始图片"
    )
    result_image = models.ImageField(
        upload_to='uploads/results/%Y/%m/%d/', 
        verbose_name="检测结果图片",
        blank=True,
        null=True
    )
    
    # 检测参数
    model_name = models.CharField(
        max_length=100, 
        default="yolo11n-seg.pt",
        verbose_name="使用的模型"
    )
    confidence_threshold = models.FloatField(
        default=0.25, 
        verbose_name="置信度阈值"
    )
    iou_threshold = models.FloatField(
        default=0.45, 
        verbose_name="IOU阈值"
    )
    image_size = models.IntegerField(
        default=640, 
        verbose_name="图像尺寸"
    )
    
    # 检测结果统计
    total_detections = models.IntegerField(
        default=0, 
        verbose_name="总检测数量"
    )
    glioma_tumor_count = models.IntegerField(
        default=0,
        verbose_name="胶质瘤数量"
    )
    meningioma_tumor_count = models.IntegerField(
        default=0,
        verbose_name="脑膜瘤数量"
    )
    pituitary_tumor_count = models.IntegerField(
        default=0,
        verbose_name="垂体瘤数量"
    )
    
    # 详细结果和性能信息
    detection_details = models.JSONField(
        default=dict, 
        verbose_name="检测详细结果",
        help_text="包含每个检测框的坐标、置信度等详细信息"
    )
    processing_time = models.FloatField(
        null=True, 
        blank=True, 
        verbose_name="处理时间(秒)"
    )
    
    # 状态字段
    STATUS_CHOICES = [
        ('pending', '等待处理'),
        ('processing', '处理中'),
        ('completed', '已完成'),
        ('failed', '处理失败'),
    ]
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        verbose_name="处理状态"
    )
    error_message = models.TextField(
        blank=True,
        null=True,
        verbose_name="错误信息"
    )
    
    class Meta:
        verbose_name = "检测记录"
        verbose_name_plural = "检测记录"
        ordering = ['-upload_time']
    
    def __str__(self):
        return f"患者{self.patient_name}的检测记录 #{self.id} - {self.upload_time.strftime('%Y-%m-%d %H:%M:%S')}"
    
    @property
    def patient_info_summary(self):
        """获取患者信息摘要"""
        gender_display = dict(self.__class__._meta.get_field('patient_gender').choices)[self.patient_gender]
        return {
            'name': self.patient_name,
            'gender': gender_display,
            'age': self.patient_age,
            'formatted': f"{self.patient_name}（{gender_display}，{self.patient_age}岁）"
        }
    
    @property
    def detection_summary(self):
        """获取检测结果摘要"""
        return {
            'total': self.total_detections,
            'glioma_tumor': self.glioma_tumor_count,
            'meningioma_tumor': self.meningioma_tumor_count,
            'pituitary_tumor': self.pituitary_tumor_count,
            'tumor_distribution': {
                'glioma_rate': round(
                    (self.glioma_tumor_count / self.total_detections * 100)
                    if self.total_detections > 0 else 0, 2
                ),
                'meningioma_rate': round(
                    (self.meningioma_tumor_count / self.total_detections * 100)
                    if self.total_detections > 0 else 0, 2
                ),
                'pituitary_rate': round(
                    (self.pituitary_tumor_count / self.total_detections * 100)
                    if self.total_detections > 0 else 0, 2
                )
            }
        }
    
    def get_detection_details_json(self):
        """获取格式化的检测详情"""
        if isinstance(self.detection_details, str):
            try:
                return json.loads(self.detection_details)
            except json.JSONDecodeError:
                return {}
        return self.detection_details or {}


class ModelConfig(models.Model):
    """模型配置模型"""
    
    name = models.CharField(
        max_length=100, 
        unique=True, 
        verbose_name="模型名称"
    )
    file_path = models.CharField(
        max_length=255, 
        verbose_name="模型文件路径"
    )
    description = models.TextField(
        blank=True, 
        verbose_name="模型描述"
    )
    is_active = models.BooleanField(
        default=True, 
        verbose_name="是否启用"
    )
    created_time = models.DateTimeField(
        auto_now_add=True, 
        verbose_name="创建时间"
    )
    
    # 模型性能参数
    accuracy = models.FloatField(
        null=True, 
        blank=True, 
        verbose_name="准确率"
    )
    inference_speed = models.FloatField(
        null=True, 
        blank=True, 
        verbose_name="推理速度(ms)"
    )
    model_size = models.FloatField(
        null=True, 
        blank=True, 
        verbose_name="模型大小(MB)"
    )
    
    class Meta:
        verbose_name = "模型配置"
        verbose_name_plural = "模型配置"
        ordering = ['-created_time']
    
    def __str__(self):
        return f"{self.name} ({'启用' if self.is_active else '禁用'})"
