"""
PDF报告生成服务
"""
import io
import os
from datetime import datetime
from pathlib import Path

from reportlab.lib import colors
from reportlab.lib.pagesizes import A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch, cm
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image, PageBreak
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT

from django.conf import settings
from django.core.files.storage import default_storage
import logging

logger = logging.getLogger(__name__)


class PDFReportService:
    """PDF报告生成服务"""
    
    def __init__(self):
        # 注册中文字体
        self.setup_fonts()
        
    def setup_fonts(self):
        """设置中文字体"""
        try:
            # 尝试使用系统中文字体
            font_paths = [
                "C:/Windows/Fonts/simhei.ttf",  # 黑体
                "C:/Windows/Fonts/simsun.ttc",  # 宋体
                "C:/Windows/Fonts/msyh.ttc",   # 微软雅黑
            ]
            
            font_registered = False
            for font_path in font_paths:
                if os.path.exists(font_path):
                    try:
                        pdfmetrics.registerFont(TTFont('SimHei', font_path))
                        font_registered = True
                        logger.info(f"成功注册中文字体: {font_path}")
                        break
                    except Exception as e:
                        logger.warning(f"注册字体失败 {font_path}: {str(e)}")
                        continue
            
            if not font_registered:
                logger.warning("未找到中文字体，将使用默认字体")
                
        except Exception as e:
            logger.error(f"字体设置失败: {str(e)}")
    
    def create_pdf_report(self, record, llm_analysis=None):
        """
        创建PDF报告
        
        Args:
            record: 检测记录对象
            llm_analysis: LLM分析结果（可选）
            
        Returns:
            bytes: PDF文件的二进制数据
        """
        try:
            # 创建PDF文档
            buffer = io.BytesIO()
            doc = SimpleDocTemplate(
                buffer,
                pagesize=A4,
                rightMargin=2*cm,
                leftMargin=2*cm,
                topMargin=2*cm,
                bottomMargin=2*cm
            )
            
            # 构建报告内容
            story = []
            
            # 添加标题和基本信息
            story.extend(self._create_header(record))
            story.append(Spacer(1, 0.5*cm))
            
            # 添加患者信息
            story.extend(self._create_patient_info(record))
            story.append(Spacer(1, 0.5*cm))
            
            # 添加检测参数
            story.extend(self._create_detection_params(record))
            story.append(Spacer(1, 0.5*cm))
            
            # 添加图像对比
            story.extend(self._create_image_comparison(record))
            story.append(Spacer(1, 0.5*cm))
            
            # 添加检测结果统计
            story.extend(self._create_detection_summary(record))
            story.append(Spacer(1, 0.5*cm))
            
            # 添加详细检测数据
            if record.detection_details:
                story.extend(self._create_detection_details(record))
                story.append(Spacer(1, 0.5*cm))
            
            # 添加LLM分析结果
            if llm_analysis:
                story.append(PageBreak())
                story.extend(self._create_llm_analysis(llm_analysis))
            
            # 添加页脚
            story.append(Spacer(1, 1*cm))
            story.extend(self._create_footer())
            
            # 生成PDF
            doc.build(story)
            buffer.seek(0)
            
            return buffer.getvalue()
            
        except Exception as e:
            logger.error(f"PDF生成失败: {str(e)}")
            raise Exception(f"PDF报告生成失败: {str(e)}")
    
    def _create_header(self, record):
        """创建报告头部"""
        styles = getSampleStyleSheet()
        
        # 自定义样式
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Title'],
            fontSize=20,
            spaceAfter=30,
            alignment=TA_CENTER,
            fontName='SimHei' if 'SimHei' in pdfmetrics.getRegisteredFontNames() else 'Helvetica-Bold'
        )
        
        subtitle_style = ParagraphStyle(
            'CustomSubtitle',
            parent=styles['Normal'],
            fontSize=12,
            spaceAfter=12,
            alignment=TA_CENTER,
            fontName='SimHei' if 'SimHei' in pdfmetrics.getRegisteredFontNames() else 'Helvetica'
        )
        
        story = []
        
        # 个性化标题（包含患者姓名）
        report_title = f"{record.patient_name}的脑肿瘤检测AI诊断报告"
        story.append(Paragraph(report_title, title_style))
        
        # 报告信息
        report_info = f"""
        <b>报告编号:</b> BTD-{record.id:06d}<br/>
        <b>检测时间:</b> {record.upload_time.strftime('%Y年%m月%d日 %H:%M:%S')}<br/>
        <b>报告生成时间:</b> {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}
        """
        story.append(Paragraph(report_info, subtitle_style))
        
        return story
    
    def _create_patient_info(self, record):
        """创建患者信息部分"""
        styles = getSampleStyleSheet()
        
        section_style = ParagraphStyle(
            'SectionTitle',
            parent=styles['Heading2'],
            fontSize=14,
            spaceAfter=12,
            fontName='SimHei' if 'SimHei' in pdfmetrics.getRegisteredFontNames() else 'Helvetica-Bold'
        )
        
        story = []
        story.append(Paragraph("患者信息", section_style))
        
        # 患者信息表格 - 调整列宽
        gender_display = dict(record.__class__._meta.get_field('patient_gender').choices)[record.patient_gender]
        
        patient_data = [
            ['患者姓名', record.patient_name],
            ['性别', gender_display],
            ['年龄', f'{record.patient_age} 岁'],
        ]
        
        # 修改列宽比例，第一列稍窄，第二列更宽
        patient_table = Table(patient_data, colWidths=[4*cm, 8*cm])
        patient_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, -1), 'SimHei' if 'SimHei' in pdfmetrics.getRegisteredFontNames() else 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('LEFTPADDING', (0, 0), (-1, -1), 8),
            ('RIGHTPADDING', (0, 0), (-1, -1), 8),
            ('TOPPADDING', (0, 0), (-1, -1), 8),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
        ]))
        
        story.append(patient_table)
        return story
    
    def _create_detection_params(self, record):
        """创建检测参数部分"""
        styles = getSampleStyleSheet()
        
        section_style = ParagraphStyle(
            'SectionTitle',
            parent=styles['Heading2'],
            fontSize=14,
            spaceAfter=12,
            fontName='SimHei' if 'SimHei' in pdfmetrics.getRegisteredFontNames() else 'Helvetica-Bold'
        )
        
        story = []
        story.append(Paragraph("检测参数", section_style))
        
        # 检测参数表格 - 调整列宽和显示格式
        params_data = [
            ['使用模型', record.model_name or '默认模型'],
            ['置信度阈值', f'{record.confidence_threshold:.2f}'],
            ['IOU阈值', f'{record.iou_threshold:.2f}'],
            ['图像尺寸', f'{record.image_size}×{record.image_size}'],
            ['处理时间', f'{record.processing_time:.2f} 秒' if record.processing_time else '未记录'],
        ]
        
        # 修改列宽比例，确保第二列有足够空间显示长文本
        params_table = Table(params_data, colWidths=[4*cm, 8*cm])
        params_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, -1), 'SimHei' if 'SimHei' in pdfmetrics.getRegisteredFontNames() else 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('LEFTPADDING', (0, 0), (-1, -1), 8),
            ('RIGHTPADDING', (0, 0), (-1, -1), 8),
            ('TOPPADDING', (0, 0), (-1, -1), 8),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
        ]))
        
        story.append(params_table)
        return story
    
    def _create_image_comparison(self, record):
        """创建图像对比部分"""
        styles = getSampleStyleSheet()
        
        section_style = ParagraphStyle(
            'SectionTitle',
            parent=styles['Heading2'],
            fontSize=14,
            spaceAfter=12,
            fontName='SimHei' if 'SimHei' in pdfmetrics.getRegisteredFontNames() else 'Helvetica-Bold'
        )
        
        story = []
        story.append(Paragraph("影像检测结果对比", section_style))
        
        try:
            images_data = []
            
            # 原始图像
            if record.original_image:
                original_path = record.original_image.path
                if os.path.exists(original_path):
                    img1 = Image(original_path, width=7*cm, height=5*cm)
                    images_data.append([img1, "原始影像"])
            
            # 检测结果图像
            if record.result_image:
                result_path = record.result_image.path
                if os.path.exists(result_path):
                    img2 = Image(result_path, width=7*cm, height=5*cm)
                    if len(images_data) > 0:
                        images_data[0].extend([img2, "检测结果"])
                    else:
                        images_data.append([img2, "检测结果"])
            
            if images_data:
                # 如果只有一张图片，创建单列表格
                if len(images_data[0]) == 2:
                    image_table = Table(images_data, colWidths=[7*cm, 7*cm])
                else:
                    image_table = Table(images_data, colWidths=[7*cm, 3*cm, 7*cm, 3*cm])
                
                image_table.setStyle(TableStyle([
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                    ('FONTNAME', (0, 0), (-1, -1), 'SimHei' if 'SimHei' in pdfmetrics.getRegisteredFontNames() else 'Helvetica'),
                    ('FONTSIZE', (0, 0), (-1, -1), 10),
                    ('TOPPADDING', (0, 0), (-1, -1), 12),
                    ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
                ]))
                
                story.append(image_table)
            else:
                story.append(Paragraph("图像文件未找到", styles['Normal']))
                
        except Exception as e:
            logger.error(f"创建图像对比失败: {str(e)}")
            story.append(Paragraph("图像加载失败", styles['Normal']))
        
        return story
    
    def _create_detection_summary(self, record):
        """创建检测结果统计"""
        styles = getSampleStyleSheet()
        
        section_style = ParagraphStyle(
            'SectionTitle',
            parent=styles['Heading2'],
            fontSize=14,
            spaceAfter=12,
            fontName='SimHei' if 'SimHei' in pdfmetrics.getRegisteredFontNames() else 'Helvetica-Bold'
        )
        
        story = []
        story.append(Paragraph("检测结果统计", section_style))
        
        # 检测统计表格
        summary_data = [
            ['检测项目', '数量', '占比'],
            ['总检测数量', str(record.total_detections), '100%'],
            ['胶质瘤', str(record.glioma_tumor_count), 
             f'{(record.glioma_tumor_count/record.total_detections*100):.1f}%' if record.total_detections > 0 else '0%'],
            ['脑膜瘤', str(record.meningioma_tumor_count), 
             f'{(record.meningioma_tumor_count/record.total_detections*100):.1f}%' if record.total_detections > 0 else '0%'],
            ['垂体瘤', str(record.pituitary_tumor_count), 
             f'{(record.pituitary_tumor_count/record.total_detections*100):.1f}%' if record.total_detections > 0 else '0%'],
        ]
        
        summary_table = Table(summary_data, colWidths=[4*cm, 2*cm, 2*cm])
        summary_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, -1), 'SimHei' if 'SimHei' in pdfmetrics.getRegisteredFontNames() else 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
        ]))
        
        story.append(summary_table)
        return story
    
    def _create_detection_details(self, record):
        """创建详细检测数据"""
        styles = getSampleStyleSheet()
        
        section_style = ParagraphStyle(
            'SectionTitle',
            parent=styles['Heading2'],
            fontSize=14,
            spaceAfter=12,
            fontName='SimHei' if 'SimHei' in pdfmetrics.getRegisteredFontNames() else 'Helvetica-Bold'
        )
        
        story = []
        story.append(Paragraph("详细检测数据", section_style))
        
        try:
            detection_details = record.get_detection_details_json()
            if detection_details:
                # 详细数据表格
                details_data = [['序号', '肿瘤类型', '置信度', '位置(x,y)', '尺寸(w,h)']]
                
                for i, detail in enumerate(detection_details, 1):
                    class_name = detail.get('class_name', '未知')
                    # 转换类别名称为中文
                    class_display = {
                        'glioma_tumor': '胶质瘤',
                        'meningioma_tumor': '脑膜瘤',
                        'pituitary_tumor': '垂体瘤'
                    }.get(class_name, class_name)

                    confidence = detail.get('confidence', 0)

                    # 获取位置和尺寸信息（支持两种格式）
                    if 'bbox' in detail:
                        # 旧格式：bbox = [x, y, w, h]
                        bbox = detail.get('bbox', [0, 0, 0, 0])
                        x_center, y_center = bbox[0], bbox[1]  # 假设bbox是中心坐标
                        x_pos, y_pos = bbox[0], bbox[1]
                        width, height = bbox[2], bbox[3]
                    else:
                        # 新格式：分别的字段
                        x_center = detail.get('x_center', 0)
                        y_center = detail.get('y_center', 0)
                        width = detail.get('width', 0)
                        height = detail.get('height', 0)

                        # 转换为左上角坐标（归一化坐标，0-1范围）
                        x_pos = x_center - width / 2
                        y_pos = y_center - height / 2

                    details_data.append([
                        str(i),
                        class_display,
                        f'{confidence:.3f}',
                        f'({x_pos:.4f}, {y_pos:.4f})',
                        f'({width:.4f}, {height:.4f})'
                    ])
                
                details_table = Table(details_data, colWidths=[1.5*cm, 2.5*cm, 2*cm, 2.5*cm, 2.5*cm])
                details_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, -1), 'SimHei' if 'SimHei' in pdfmetrics.getRegisteredFontNames() else 'Helvetica'),
                    ('FONTSIZE', (0, 0), (-1, -1), 9),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black),
                    ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ]))
                
                story.append(details_table)
            else:
                story.append(Paragraph("暂无详细检测数据", styles['Normal']))
                
        except Exception as e:
            logger.error(f"创建详细检测数据失败: {str(e)}")
            story.append(Paragraph("详细检测数据加载失败", styles['Normal']))
        
        return story
    
    def _create_llm_analysis(self, llm_analysis):
        """创建LLM分析部分"""
        styles = getSampleStyleSheet()
        
        section_style = ParagraphStyle(
            'SectionTitle',
            parent=styles['Heading2'],
            fontSize=16,
            spaceAfter=16,
            spaceBefore=12,
            fontName='SimHei' if 'SimHei' in pdfmetrics.getRegisteredFontNames() else 'Helvetica-Bold',
            textColor=colors.darkblue
        )
        
        content_style = ParagraphStyle(
            'Content',
            parent=styles['Normal'],
            fontSize=11,
            spaceAfter=8,
            spaceBefore=4,
            leftIndent=12,
            rightIndent=12,
            fontName='SimHei' if 'SimHei' in pdfmetrics.getRegisteredFontNames() else 'Helvetica',
            leading=16
        )
        
        # 标题样式
        subtitle_style = ParagraphStyle(
            'Subtitle',
            parent=styles['Normal'],
            fontSize=12,
            spaceAfter=6,
            spaceBefore=8,
            leftIndent=6,
            fontName='SimHei' if 'SimHei' in pdfmetrics.getRegisteredFontNames() else 'Helvetica-Bold',
            textColor=colors.darkgreen
        )
        
        story = []
        story.append(Paragraph("🤖 AI智能分析报告", section_style))
        
        # 添加分析报告说明
        intro_text = "以下是基于深度学习算法对检测结果进行的智能分析和医学建议："
        story.append(Paragraph(intro_text, subtitle_style))
        story.append(Spacer(1, 0.3*cm))
        
        # 处理分析文本
        if llm_analysis:
            # 清理和格式化分析文本
            analysis_text = llm_analysis.strip()
            
            # 处理换行和格式
            # 将markdown样式的标题转换为适当的段落
            lines = analysis_text.split('\n')
            processed_lines = []
            
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                    
                # 处理markdown标题
                if line.startswith('###'):
                    # 三级标题
                    title = line.replace('###', '').strip()
                    processed_lines.append(f'<b><font color="darkgreen">{title}</font></b>')
                elif line.startswith('##'):
                    # 二级标题
                    title = line.replace('##', '').strip()
                    processed_lines.append(f'<b><font color="darkblue" size="12">{title}</font></b>')
                elif line.startswith('#'):
                    # 一级标题
                    title = line.replace('#', '').strip()
                    processed_lines.append(f'<b><font color="red" size="14">{title}</font></b>')
                elif line.startswith('- ') or line.startswith('* '):
                    # 列表项
                    item = line[2:].strip()
                    processed_lines.append(f'• {item}')
                elif line.startswith('**') and line.endswith('**'):
                    # 粗体文本
                    text = line[2:-2]
                    processed_lines.append(f'<b>{text}</b>')
                else:
                    # 普通文本
                    processed_lines.append(line)
            
            # 合并处理后的文本
            formatted_text = '<br/>'.join(processed_lines)
            
            # 分段显示，避免单个段落过长
            paragraphs = formatted_text.split('<br/><br/>')
            
            for para in paragraphs:
                if para.strip():
                    story.append(Paragraph(para.strip(), content_style))
                    story.append(Spacer(1, 0.2*cm))
        else:
            # 如果没有分析内容，显示提示信息
            no_analysis_text = "暂无AI分析结果。如需获取详细的AI诊断建议，请在检测结果页面点击'AI分析'按钮。"
            story.append(Paragraph(no_analysis_text, content_style))
        
        # 添加免责声明
        story.append(Spacer(1, 0.5*cm))
        disclaimer_style = ParagraphStyle(
            'Disclaimer',
            parent=styles['Normal'],
            fontSize=9,
            leftIndent=12,
            rightIndent=12,
            fontName='SimHei' if 'SimHei' in pdfmetrics.getRegisteredFontNames() else 'Helvetica',
            textColor=colors.grey,
            borderColor=colors.lightgrey,
            borderWidth=1,
            borderPadding=8
        )
        
        disclaimer_text = """
        <b>⚠️ 重要提示：</b><br/>
        本AI分析报告基于深度学习算法生成，仅供医学参考，不能替代专业医师的临床诊断。
        请务必结合患者的临床症状、病史和其他检查结果进行综合判断。
        如有疑问，请及时咨询专业医师。
        """
        
        story.append(Paragraph(disclaimer_text, disclaimer_style))
        
        return story
    
    def _create_footer(self):
        """创建页脚"""
        styles = getSampleStyleSheet()
        
        footer_style = ParagraphStyle(
            'Footer',
            parent=styles['Normal'],
            fontSize=8,
            alignment=TA_CENTER,
            fontName='SimHei' if 'SimHei' in pdfmetrics.getRegisteredFontNames() else 'Helvetica'
        )
        
        story = []
        story.append(Spacer(1, 1*cm))
        
        footer_text = f"""
        <i>本报告由脑肿瘤检测AI系统自动生成</i><br/>
        <i>生成时间: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}</i><br/>
        <i>注意：本报告仅供参考，不能替代专业医师诊断</i>
        """
        
        story.append(Paragraph(footer_text, footer_style))
        
        return story 