from django import template

register = template.Library()

@register.filter
def get_center_x(detection):
    """获取中心X坐标"""
    if isinstance(detection, dict):
        return detection.get('x_center', 0)
    return 0

@register.filter
def get_center_y(detection):
    """获取中心Y坐标"""
    if isinstance(detection, dict):
        return detection.get('y_center', 0)
    return 0

@register.filter
def get_width(detection):
    """获取宽度"""
    if isinstance(detection, dict):
        return detection.get('width', 0)
    return 0

@register.filter
def get_height(detection):
    """获取高度"""
    if isinstance(detection, dict):
        return detection.get('height', 0)
    return 0

@register.filter
def get_position_x(detection):
    """获取左上角X坐标"""
    if isinstance(detection, dict):
        x_center = detection.get('x_center', 0)
        width = detection.get('width', 0)
        return x_center - width / 2
    return 0

@register.filter
def get_position_y(detection):
    """获取左上角Y坐标"""
    if isinstance(detection, dict):
        y_center = detection.get('y_center', 0)
        height = detection.get('height', 0)
        return y_center - height / 2
    return 0

@register.filter
def format_coordinate(value):
    """格式化坐标值"""
    try:
        return f"{float(value):.4f}"
    except (ValueError, TypeError):
        return "0.0000"
