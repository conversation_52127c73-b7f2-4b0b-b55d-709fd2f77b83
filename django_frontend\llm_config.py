"""
大模型API配置文件
"""
import os

# SiliconFlow API配置
SILICONFLOW_CONFIG = {
    'api_key': os.getenv('SILICONFLOW_API_KEY', 'sk-pzpwbqylqarvqzjfjlgnrpyeipbabekklpwfghklwtuesjfi'),
    'base_url': 'https://api.siliconflow.cn/v1/chat/completions',
    'timeout': 120,  # 增加到60秒
    'max_tokens': 2000,
    'temperature': 0.7
}

# 可用的SiliconFlow模型列表
AVAILABLE_MODELS = [
    {
        'value': 'Qwen/QwQ-32B',
        'name': 'Qwen QwQ-32B',
        'description': '强大的推理模型，适合复杂的脑肿瘤分析'
    },
    {
        'value': 'Qwen/Qwen2.5-7B-Instruct',
        'name': 'Qwen 2.5-7B-Instruct',
        'description': '快速响应，适合一般的医学影像分析'
    },
    {
        'value': 'Qwen/Qwen2.5-14B-Instruct',
        'name': 'Qwen 2.5-14B-Instruct',
        'description': '平衡性能，综合诊断分析能力强'
    },
    {
        'value': 'deepseek-ai/DeepSeek-V2.5',
        'name': 'DeepSeek V2.5',
        'description': '深度思考模型，适合专业医学分析'
    }
]

# 模型映射配置 - 将前端选择的模型映射到SiliconFlow的实际模型
MODEL_MAPPING = {
    'gpt-4': 'Qwen/QwQ-32B',
    'gpt-3.5-turbo': 'Qwen/Qwen2.5-7B-Instruct',
    'claude-3-sonnet': 'Qwen/QwQ-32B',
    'gemini-pro': 'Qwen/QwQ-32B'
}

# 默认模型
DEFAULT_MODEL = 'Qwen/QwQ-32B'

# 系统提示词
SYSTEM_PROMPT = "你是一个专业的脑肿瘤检测分析专家，请用中文回答，语言专业且易懂。"

# API重试配置
RETRY_CONFIG = {
    'max_retries': 3,
    'retry_delay': 1,  # 秒
    'backoff_factor': 2
}

# 日志配置
LOGGING_CONFIG = {
    'log_requests': True,
    'log_responses': True,
    'log_errors': True
}

# 分析模板配置
ANALYSIS_TEMPLATES = {
    'default': "基于检测结果，请分析这张医学影像中的脑肿瘤检测情况，并给出专业的诊断建议。",
    'detailed': "请详细分析检测到的脑肿瘤类型、位置、大小等特征，并提供临床诊断建议。",
    'comparison': "请比较不同类型脑肿瘤的检测结果，分析其临床意义和治疗建议。",
    'risk_assessment': "请基于检测结果进行风险评估，分析肿瘤的恶性程度和预后情况。"
}

# 响应格式配置
RESPONSE_FORMAT = {
    'include_confidence': True,
    'include_recommendations': True,
    'include_risk_level': True,
    'max_response_length': 2000
}
