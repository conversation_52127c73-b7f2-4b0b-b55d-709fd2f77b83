INFO 2025-07-02 11:48:01,597 autoreload 3624 29908 Watching for file changes with StatReloader
INFO 2025-07-02 11:50:04,736 autoreload 2676 24556 Watching for file changes with StatReloader
ERROR 2025-07-02 11:50:08,867 log 2676 21044 Internal Server Error: /
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\utils\decorators.py", line 192, in _view_wrapper
    result = _process_exception(request, e)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\utils\decorators.py", line 190, in _view_wrapper
    response = view_func(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\views\decorators\cache.py", line 80, in _view_wrapper
    response = view_func(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\BTD_new\BrainTumorDetection\django_frontend\detection\auth_views.py", line 58, in welcome_view
    return render(request, 'auth/welcome.html')
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'index' not found. 'index' is not a valid view function or pattern name.
ERROR 2025-07-02 11:50:08,878 basehttp 2676 21044 "GET / HTTP/1.1" 500 159909
WARNING 2025-07-02 11:50:09,084 log 2676 21044 Not Found: /favicon.ico
WARNING 2025-07-02 11:50:09,085 basehttp 2676 21044 "GET /favicon.ico HTTP/1.1" 404 4408
INFO 2025-07-02 11:50:53,523 autoreload 24544 4576 Watching for file changes with StatReloader
ERROR 2025-07-02 11:50:59,967 log 24544 31656 Internal Server Error: /
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\utils\decorators.py", line 192, in _view_wrapper
    result = _process_exception(request, e)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\utils\decorators.py", line 190, in _view_wrapper
    response = view_func(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\views\decorators\cache.py", line 80, in _view_wrapper
    response = view_func(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\BTD_new\BrainTumorDetection\django_frontend\detection\auth_views.py", line 58, in welcome_view
    return render(request, 'auth/welcome.html')
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'index' not found. 'index' is not a valid view function or pattern name.
ERROR 2025-07-02 11:50:59,974 basehttp 24544 31656 "GET / HTTP/1.1" 500 157366
WARNING 2025-07-02 11:51:00,358 log 24544 31656 Not Found: /favicon.ico
WARNING 2025-07-02 11:51:00,359 basehttp 24544 31656 "GET /favicon.ico HTTP/1.1" 404 4408
INFO 2025-07-02 11:53:37,450 autoreload 14900 9516 Watching for file changes with StatReloader
INFO 2025-07-02 11:53:46,207 basehttp 14900 28244 "GET / HTTP/1.1" 200 9889
INFO 2025-07-02 11:53:56,811 basehttp 14900 28244 "GET /login/ HTTP/1.1" 200 10704
WARNING 2025-07-02 11:54:11,016 log 14900 28244 Forbidden (CSRF token missing.): /login/
WARNING 2025-07-02 11:54:11,017 basehttp 14900 28244 "POST /login/ HTTP/1.1" 403 2486
INFO 2025-07-02 11:54:15,725 basehttp 14900 28244 "GET /login/ HTTP/1.1" 200 10704
WARNING 2025-07-02 11:54:33,598 log 14900 28244 Forbidden (CSRF token missing.): /login/
WARNING 2025-07-02 11:54:33,599 basehttp 14900 28244 "POST /login/ HTTP/1.1" 403 2486
INFO 2025-07-02 11:54:36,300 basehttp 14900 28244 "GET /login/ HTTP/1.1" 200 10704
WARNING 2025-07-02 11:54:47,134 log 14900 28244 Forbidden (CSRF token missing.): /login/
WARNING 2025-07-02 11:54:47,137 basehttp 14900 28244 "POST /login/ HTTP/1.1" 403 2486
INFO 2025-07-02 11:54:49,238 basehttp 14900 28244 "GET /login/ HTTP/1.1" 200 10704
INFO 2025-07-02 11:54:50,786 basehttp 14900 28244 "GET / HTTP/1.1" 200 9889
INFO 2025-07-02 11:54:53,102 basehttp 14900 28244 "GET /register/ HTTP/1.1" 200 16237
INFO 2025-07-02 11:54:57,874 basehttp 14900 28244 "GET /login/ HTTP/1.1" 200 10704
INFO 2025-07-02 11:54:59,525 autoreload 14900 9516 D:\BTD_new\BrainTumorDetection\django_frontend\brain_tumor_detection\settings.py changed, reloading.
INFO 2025-07-02 11:55:00,609 autoreload 27936 21000 Watching for file changes with StatReloader
INFO 2025-07-02 11:55:04,048 basehttp 27936 13512 "GET /register/ HTTP/1.1" 200 16237
INFO 2025-07-02 11:55:32,823 autoreload 20904 11120 Watching for file changes with StatReloader
INFO 2025-07-02 11:55:33,956 basehttp 20904 27908 "GET /login/ HTTP/1.1" 200 10704
INFO 2025-07-02 11:55:34,933 basehttp 20904 27908 "GET / HTTP/1.1" 200 9889
INFO 2025-07-02 11:55:37,215 basehttp 20904 27908 "GET /login/ HTTP/1.1" 200 10704
WARNING 2025-07-02 11:55:45,204 log 20904 27908 Forbidden (CSRF token missing.): /login/
WARNING 2025-07-02 11:55:45,208 basehttp 20904 27908 "POST /login/ HTTP/1.1" 403 2486
ERROR 2025-07-02 11:56:58,527 log 1796 10552 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\utils\deprecation.py", line 119, in __call__
    response = self.process_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
           ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\http\request.py", line 191, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
ERROR 2025-07-02 11:56:58,550 log 1796 10552 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\utils\deprecation.py", line 119, in __call__
    response = self.process_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
           ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\http\request.py", line 191, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
ERROR 2025-07-02 11:56:58,594 log 1796 10552 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\utils\deprecation.py", line 119, in __call__
    response = self.process_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
           ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\http\request.py", line 191, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
INFO 2025-07-02 11:57:17,544 autoreload 20904 11120 D:\BTD_new\BrainTumorDetection\django_frontend\brain_tumor_detection\settings.py changed, reloading.
INFO 2025-07-02 11:57:18,796 autoreload 17784 23084 Watching for file changes with StatReloader
INFO 2025-07-02 11:57:51,121 autoreload 23644 27784 Watching for file changes with StatReloader
INFO 2025-07-02 11:58:25,663 basehttp 23644 10572 "GET / HTTP/1.1" 200 9889
INFO 2025-07-02 11:58:27,532 basehttp 23644 10572 "GET /login/ HTTP/1.1" 200 10704
WARNING 2025-07-02 11:58:33,727 log 23644 10572 Forbidden (CSRF token missing.): /login/
WARNING 2025-07-02 11:58:33,729 basehttp 23644 10572 "POST /login/ HTTP/1.1" 403 2486
INFO 2025-07-02 11:59:22,050 basehttp 23644 17024 "GET / HTTP/1.1" 200 9889
INFO 2025-07-02 11:59:24,107 basehttp 23644 28708 "GET / HTTP/1.1" 200 9889
INFO 2025-07-02 11:59:26,193 basehttp 23644 30248 "GET /login/ HTTP/1.1" 200 10704
INFO 2025-07-02 11:59:28,266 basehttp 23644 9968 "GET /register/ HTTP/1.1" 200 16237
INFO 2025-07-02 11:59:58,684 basehttp 23644 10572 "GET /login/ HTTP/1.1" 200 10704
INFO 2025-07-02 12:00:41,936 basehttp 23644 10572 "GET /login/ HTTP/1.1" 200 10704
WARNING 2025-07-02 12:00:50,766 log 23644 10572 Forbidden (CSRF token missing.): /login/
WARNING 2025-07-02 12:00:50,769 basehttp 23644 10572 "POST /login/ HTTP/1.1" 403 2486
INFO 2025-07-02 14:34:16,192 basehttp 23644 37492 "GET / HTTP/1.1" 200 9889
INFO 2025-07-02 14:34:18,698 basehttp 23644 37492 "GET /login/ HTTP/1.1" 200 10704
WARNING 2025-07-02 14:34:27,267 log 23644 37492 Forbidden (CSRF token missing.): /login/
WARNING 2025-07-02 14:34:27,269 basehttp 23644 37492 "POST /login/ HTTP/1.1" 403 2486
INFO 2025-07-02 14:38:21,752 autoreload 23644 27784 D:\BTD_new\BrainTumorDetection\django_frontend\detection\auth_views.py changed, reloading.
INFO 2025-07-02 14:38:23,104 autoreload 2504 29496 Watching for file changes with StatReloader
INFO 2025-07-02 14:38:39,034 autoreload 2504 29496 D:\BTD_new\BrainTumorDetection\django_frontend\detection\auth_views.py changed, reloading.
INFO 2025-07-02 14:38:40,161 autoreload 23676 16256 Watching for file changes with StatReloader
INFO 2025-07-02 14:40:30,047 autoreload 23676 16256 D:\BTD_new\BrainTumorDetection\django_frontend\detection\auth_views.py changed, reloading.
INFO 2025-07-02 14:40:31,199 autoreload 34564 14136 Watching for file changes with StatReloader
WARNING 2025-07-02 14:41:32,043 log 17252 34160 Forbidden (CSRF token missing.): /login/
INFO 2025-07-02 14:41:48,776 autoreload 34564 14136 D:\BTD_new\BrainTumorDetection\django_frontend\brain_tumor_detection\settings.py changed, reloading.
INFO 2025-07-02 14:41:49,947 autoreload 30436 6396 Watching for file changes with StatReloader
INFO 2025-07-02 14:43:34,668 autoreload 28644 18508 Watching for file changes with StatReloader
INFO 2025-07-02 14:44:27,085 autoreload 28644 18508 D:\BTD_new\BrainTumorDetection\django_frontend\detection\auth_views.py changed, reloading.
INFO 2025-07-02 14:44:28,256 autoreload 37096 35220 Watching for file changes with StatReloader
INFO 2025-07-02 14:44:46,294 autoreload 37096 35220 D:\BTD_new\BrainTumorDetection\django_frontend\detection\auth_urls.py changed, reloading.
INFO 2025-07-02 14:44:47,466 autoreload 15048 28444 Watching for file changes with StatReloader
INFO 2025-07-02 14:45:05,612 basehttp 15048 30640 "GET /test-csrf/ HTTP/1.1" 200 4668
INFO 2025-07-02 14:45:12,624 basehttp 15048 30640 "POST /login/ HTTP/1.1" 302 0
ERROR 2025-07-02 14:45:12,812 log 15048 30640 Internal Server Error: /detection/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\BTD_new\BrainTumorDetection\django_frontend\detection\views.py", line 71, in index
    return render(request, 'detection/index.html', context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'detect' not found. 'detect' is not a valid view function or pattern name.
ERROR 2025-07-02 14:45:12,840 basehttp 15048 30640 "GET /detection/ HTTP/1.1" 500 184822
WARNING 2025-07-02 14:45:17,540 log 15048 30640 Forbidden (CSRF token from POST incorrect.): /login/
WARNING 2025-07-02 14:45:17,542 basehttp 15048 30640 "POST /login/ HTTP/1.1" 403 2498
INFO 2025-07-02 14:46:29,151 autoreload 15048 28444 D:\BTD_new\BrainTumorDetection\django_frontend\detection\auth_views.py changed, reloading.
INFO 2025-07-02 14:46:30,334 autoreload 24472 2056 Watching for file changes with StatReloader
INFO 2025-07-02 14:46:43,881 autoreload 24472 2056 D:\BTD_new\BrainTumorDetection\django_frontend\detection\auth_urls.py changed, reloading.
INFO 2025-07-02 14:46:45,194 autoreload 31112 25356 Watching for file changes with StatReloader
INFO 2025-07-02 14:46:58,719 basehttp 31112 26640 "GET /simple-login/ HTTP/1.1" 200 1526
INFO 2025-07-02 14:47:50,714 basehttp 31112 26640 "GET /test-csrf/ HTTP/1.1" 200 4668
INFO 2025-07-02 14:47:52,135 basehttp 31112 26640 "GET /test-csrf/ HTTP/1.1" 200 4668
INFO 2025-07-02 14:47:53,601 basehttp 31112 26640 "GET /test-csrf/ HTTP/1.1" 200 4668
INFO 2025-07-02 14:47:54,298 basehttp 31112 26640 "GET /test-csrf/ HTTP/1.1" 200 4668
INFO 2025-07-02 14:47:57,785 basehttp 31112 26640 "POST /login/ HTTP/1.1" 302 0
ERROR 2025-07-02 14:47:57,858 log 31112 26640 Internal Server Error: /detection/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\BTD_new\BrainTumorDetection\django_frontend\detection\views.py", line 71, in index
    return render(request, 'detection/index.html', context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'detect' not found. 'detect' is not a valid view function or pattern name.
ERROR 2025-07-02 14:47:57,866 basehttp 31112 26640 "GET /detection/ HTTP/1.1" 500 184547
INFO 2025-07-02 14:48:00,660 basehttp 31112 26640 "GET /login/ HTTP/1.1" 302 0
ERROR 2025-07-02 14:48:00,808 log 31112 26640 Internal Server Error: /detection/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\BTD_new\BrainTumorDetection\django_frontend\detection\views.py", line 71, in index
    return render(request, 'detection/index.html', context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'detect' not found. 'detect' is not a valid view function or pattern name.
ERROR 2025-07-02 14:48:00,817 basehttp 31112 26640 "GET /detection/ HTTP/1.1" 500 184958
INFO 2025-07-02 14:48:24,933 autoreload 31112 25356 D:\BTD_new\BrainTumorDetection\django_frontend\detection\urls.py changed, reloading.
