import os
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'django_frontend.settings')
django.setup()

from detection.models import DetectionRecord

# Get latest record
record = DetectionRecord.objects.order_by('-id').first()
print(f"Record ID: {record.id}, Patient: {record.patient_name}")

# Get detection details
details = record.get_detection_details_json()
print(f"Detection count: {len(details)}")

if details:
    detail = details[0]
    print(f"Class: {detail['class_name']}")
    print(f"Confidence: {detail['confidence']:.4f}")
    print(f"Center: ({detail['x_center']:.4f}, {detail['y_center']:.4f})")
    print(f"Size: ({detail['width']:.4f}, {detail['height']:.4f})")
    
    # Calculate position for PDF display
    x_pos = detail['x_center'] - detail['width'] / 2
    y_pos = detail['y_center'] - detail['height'] / 2
    
    x_percent = x_pos * 100
    y_percent = y_pos * 100
    w_percent = detail['width'] * 100
    h_percent = detail['height'] * 100
    
    print(f"Position (percent): ({x_percent:.1f}%, {y_percent:.1f}%)")
    print(f"Size (percent): ({w_percent:.1f}%, {h_percent:.1f}%)")
    
    print("\nThis data should now appear correctly in the PDF report!")
