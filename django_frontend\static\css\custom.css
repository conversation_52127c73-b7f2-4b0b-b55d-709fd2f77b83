/* 自定义样式 */

/* 上传区域样式 */
.upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 10px;
    padding: 40px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    background-color: #fafafa;
}

.upload-area:hover {
    border-color: #0d6efd;
    background-color: #f8f9fa;
    transform: translateY(-2px);
}

.upload-area.dragover {
    border-color: #0d6efd;
    background-color: #e7f3ff;
    box-shadow: 0 0 20px rgba(13, 110, 253, 0.2);
}

/* 隐藏默认文件输入 */
.upload-area input[type="file"] {
    display: none;
}

/* 预览图片样式 */
.preview-image {
    max-width: 100%;
    max-height: 300px;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.preview-image:hover {
    transform: scale(1.02);
}

/* 检测卡片样式 */
.detection-card {
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.detection-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    border-color: #0d6efd;
}

/* 状态徽章样式 */
.status-badge {
    font-size: 0.8em;
    font-weight: 500;
}

/* 进度条容器 */
.progress-container {
    display: none;
    margin-top: 20px;
}

.progress {
    height: 8px;
    border-radius: 4px;
    overflow: hidden;
}

.progress-bar {
    transition: width 0.3s ease;
}

/* 结果对比样式 */
.result-comparison {
    display: flex;
    gap: 20px;
    align-items: flex-start;
}

.result-comparison > div {
    flex: 1;
}

@media (max-width: 768px) {
    .result-comparison {
        flex-direction: column;
        gap: 15px;
    }
}

/* 统计卡片样式 */
.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
}

.stats-card .card-body {
    padding: 1.5rem;
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: bold;
    font-size: 1.3rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: #fff !important;
}

/* 按钮样式增强 */
.btn {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

.btn-success {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    border: none;
}

.btn-danger {
    background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
    border: none;
}

.btn-warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    border: none;
}

/* 卡片样式增强 */
.card {
    border: 1px solid #e9ecef;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    border-radius: 10px 10px 0 0 !important;
}

/* 表格样式 */
.table {
    border-radius: 8px;
    overflow: hidden;
}

.table thead th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
}

/* 模态框样式 */
.modal-content {
    border-radius: 10px;
    border: none;
    box-shadow: 0 10px 40px rgba(0,0,0,0.2);
}

.modal-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    border-radius: 10px 10px 0 0;
}

/* 分页样式 */
.pagination .page-link {
    border-radius: 6px;
    margin: 0 2px;
    border: 1px solid #dee2e6;
    color: #6c757d;
    transition: all 0.3s ease;
}

.pagination .page-link:hover {
    background-color: #e9ecef;
    border-color: #adb5bd;
    transform: translateY(-1px);
}

.pagination .page-item.active .page-link {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: #667eea;
}

/* 徽章样式 */
.badge {
    font-weight: 500;
    padding: 0.5em 0.8em;
    border-radius: 6px;
}

/* 加载动画 */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 响应式调整 */
@media (max-width: 576px) {
    .container {
        padding-left: 15px;
        padding-right: 15px;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .btn {
        font-size: 0.9rem;
        padding: 0.5rem 1rem;
    }
    
    .upload-area {
        padding: 20px;
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    .upload-area {
        background-color: #2d3748;
        border-color: #4a5568;
        color: #e2e8f0;
    }
    
    .upload-area:hover {
        background-color: #374151;
        border-color: #667eea;
    }
}

/* 患者信息区域样式 */
.patient-info-section {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
}

.patient-info-display {
    background-color: #fff;
    border-radius: 6px;
    padding: 15px;
}

.section-title {
    color: #495057;
    font-weight: 600;
    margin-bottom: 15px;
}

/* 必填字段样式 */
.form-label.required::after {
    content: " *";
    color: #dc3545;
    font-weight: bold;
}

/* 患者信息输入框样式 */
.patient-info-section .form-control,
.patient-info-section .form-select {
    border: 2px solid #e9ecef;
    border-radius: 6px;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.patient-info-section .form-control:focus,
.patient-info-section .form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* 检测参数区域样式 */
.detection-params-section {
    background-color: #f1f3f4;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
}

/* 患者信息显示卡片样式 */
.patient-info-display .badge {
    font-size: 0.8rem;
    padding: 0.4rem 0.6rem;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .patient-info-section,
    .detection-params-section {
        padding: 15px;
        margin: 15px 0;
    }
    
    .section-title {
        font-size: 1rem;
    }
}
