<!DOCTYPE html>
<html>
<head>
    <title>简化登录测试</title>
    <meta charset="UTF-8">
</head>
<body>
    <h2>简化登录测试</h2>
    <form method="post" action="/login/">
        {% csrf_token %}
        <p>
            <label>用户名:</label><br>
            <input type="text" name="username" value="admin" required>
        </p>
        <p>
            <label>密码:</label><br>
            <input type="password" name="password" value="admin123" required>
        </p>
        <p>
            <input type="submit" value="登录">
        </p>
    </form>
    
    <script>
        // 显示CSRF信息
        document.addEventListener('DOMContentLoaded', function() {
            const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]');
            if (csrfToken) {
                console.log('CSRF Token:', csrfToken.value);
                const info = document.createElement('p');
                info.innerHTML = '<strong>CSRF Token:</strong> ' + csrfToken.value.substring(0, 20) + '...';
                document.body.appendChild(info);
            } else {
                console.error('CSRF Token not found!');
                const error = document.createElement('p');
                error.innerHTML = '<strong style="color:red;">错误: CSRF Token未找到!</strong>';
                document.body.appendChild(error);
            }
        });
    </script>
</body>
</html>