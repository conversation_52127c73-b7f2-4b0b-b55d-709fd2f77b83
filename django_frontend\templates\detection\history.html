{% extends 'base.html' %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <!-- 页面标题和操作 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>
                <i class="fas fa-history"></i> 检测历史记录
            </h2>
            <a href="{% url 'detection:index' %}" class="btn btn-primary">
                <i class="fas fa-home"></i> 返回首页
            </a>
        </div>
        
        <!-- 搜索和筛选 -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="get" class="row g-3">
                    <div class="col-md-4">
                        <label for="search" class="form-label">搜索</label>
                        <input type="text" class="form-control" id="search" name="search" 
                               value="{{ search_query }}" placeholder="输入记录ID或模型名称">
                    </div>
                    <div class="col-md-3">
                        <label for="status" class="form-label">状态筛选</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">全部状态</option>
                            {% for value, label in status_choices %}
                            <option value="{{ value }}" {% if status_filter == value %}selected{% endif %}>
                                {{ label }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3 d-flex align-items-end">
                        <button type="submit" class="btn btn-outline-primary me-2">
                            <i class="fas fa-search"></i> 搜索
                        </button>
                        <a href="{% url 'detection:history' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-sync"></i> 重置筛选
                        </a>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- 检测记录列表 -->
        {% if page_obj %}
        <div class="row">
            {% for record in page_obj %}
            <div class="col-lg-6 col-xl-4 mb-4">
                <div class="card detection-card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <small class="text-muted">#{{ record.id }}</small>
                        <span class="badge status-badge
                            {% if record.status == 'completed' %}bg-success
                            {% elif record.status == 'failed' %}bg-danger
                            {% elif record.status == 'processing' %}bg-warning
                            {% else %}bg-secondary{% endif %}">
                            {% if record.status == 'completed' %}已完成
                            {% elif record.status == 'failed' %}失败
                            {% elif record.status == 'processing' %}处理中
                            {% else %}等待中{% endif %}
                        </span>
                    </div>
                    
                    <div class="card-body">
                        <!-- 图片预览 -->
                        <div class="text-center mb-3">
                            {% if record.result_image %}
                                <img src="{{ record.result_image.url }}" 
                                     class="img-fluid rounded" 
                                     style="max-height: 150px; object-fit: cover;"
                                     alt="检测结果">
                            {% elif record.original_image %}
                                <img src="{{ record.original_image.url }}" 
                                     class="img-fluid rounded" 
                                     style="max-height: 150px; object-fit: cover;"
                                     alt="原始图片">
                            {% else %}
                                <div class="bg-light rounded d-flex align-items-center justify-content-center" 
                                     style="height: 150px;">
                                    <i class="fas fa-image fa-2x text-muted"></i>
                                </div>
                            {% endif %}
                        </div>
                        
                        <!-- 检测信息 -->
                        <div class="mb-3">
                            <div class="row text-center">
                                <div class="col-3">
                                    <div class="border-end">
                                        <strong class="text-primary">{{ record.total_detections }}</strong>
                                        <br><small class="text-muted">总数</small>
                                    </div>
                                </div>
                                <div class="col-3">
                                    <div class="border-end">
                                        <strong class="text-danger">{{ record.glioma_tumor_count }}</strong>
                                        <br><small class="text-muted">胶质瘤</small>
                                    </div>
                                </div>
                                <div class="col-3">
                                    <div class="border-end">
                                        <strong class="text-warning">{{ record.meningioma_tumor_count }}</strong>
                                        <br><small class="text-muted">脑膜瘤</small>
                                    </div>
                                </div>
                                <div class="col-3">
                                    <strong class="text-info">{{ record.pituitary_tumor_count }}</strong>
                                    <br><small class="text-muted">垂体瘤</small>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 详细信息 -->
                        <div class="small text-muted mb-3">
                            <div class="mb-1">
                                <i class="fas fa-robot"></i> {{ record.model_name }}
                            </div>
                            <div class="mb-1">
                                <i class="fas fa-clock"></i> {{ record.upload_time|date:"m-d H:i" }}
                            </div>
                            {% if record.processing_time %}
                            <div class="mb-1">
                                <i class="fas fa-stopwatch"></i> {{ record.processing_time|floatformat:2 }}s
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="card-footer">
                        <div class="d-flex justify-content-between">
                            {% if record.status == 'completed' %}
                                <a href="{% url 'detection:detection_result' record.id %}" 
                                   class="btn btn-sm btn-primary">
                                    <i class="fas fa-eye"></i> 查看
                                </a>
                            {% else %}
                                <span class="btn btn-sm btn-secondary disabled">
                                    <i class="fas fa-hourglass-half"></i> 处理中
                                </span>
                            {% endif %}
                            
                            <form method="post" action="{% url 'detection:delete_record' record.id %}" 
                                  onsubmit="return confirm('确定要删除这条记录吗？');"
                                  class="d-inline">
                                {% csrf_token %}
                                <button type="submit" class="btn btn-sm btn-danger">
                                    <i class="fas fa-trash"></i> 删除
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        
        <!-- 分页 -->
        {% if page_obj.has_other_pages %}
        <nav aria-label="检测记录分页">
            <ul class="pagination justify-content-center">
                {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">
                            <i class="fas fa-angle-double-left"></i>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">
                            <i class="fas fa-angle-left"></i>
                        </a>
                    </li>
                {% endif %}
                
                {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                        <li class="page-item active">
                            <span class="page-link">{{ num }}</span>
                        </li>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">{{ num }}</a>
                        </li>
                    {% endif %}
                {% endfor %}
                
                {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">
                            <i class="fas fa-angle-right"></i>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">
                            <i class="fas fa-angle-double-right"></i>
                        </a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
        
        {% else %}
        <!-- 空状态 -->
        <div class="text-center py-5">
            <i class="fas fa-inbox fa-4x text-muted mb-3"></i>
            <h4 class="text-muted">暂无检测记录</h4>
            <p class="text-muted">开始您的第一次脑肿瘤检测</p>
            <a href="{% url 'detection:index' %}" class="btn btn-primary">
                <i class="fas fa-home"></i> 返回首页
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 自动刷新处理中的记录
    const processingCards = $('.card').has('.bg-warning, .bg-secondary');
    if (processingCards.length > 0) {
        setTimeout(function() {
            location.reload();
        }, 5000); // 5秒后刷新页面
    }
});
</script>
{% endblock %}
