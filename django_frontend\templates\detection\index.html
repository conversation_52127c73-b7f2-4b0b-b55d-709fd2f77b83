{% extends 'base.html' %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<!-- 欢迎横幅 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card bg-primary text-white">
            <div class="card-body text-center py-5">
                <h1 class="display-4 mb-3">
                    <i class="fas fa-brain me-3"></i>脑肿瘤检测系统
                </h1>
                <p class="lead mb-4">基于YOLO11深度学习模型的智能脑肿瘤检测系统</p>
                <a href="{% url 'detection:detect' %}" class="btn btn-light btn-lg me-3">
                    <i class="fas fa-upload"></i> 开始检测
                </a>
                <a href="{% url 'detection:history' %}" class="btn btn-outline-light btn-lg">
                    <i class="fas fa-history"></i> 查看历史
                </a>
            </div>
        </div>
    </div>
</div>

<!-- 系统功能介绍 -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card h-100 text-center">
            <div class="card-body">
                <div class="mb-3">
                    <i class="fas fa-brain fa-3x text-primary"></i>
                </div>
                <h5 class="card-title">AI智能检测</h5>
                <p class="card-text">采用最新YOLO11模型，准确识别脑部肿瘤类型，支持胶质瘤、脑膜瘤、垂体瘤检测</p>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card h-100 text-center">
            <div class="card-body">
                <div class="mb-3">
                    <i class="fas fa-tachometer-alt fa-3x text-success"></i>
                </div>
                <h5 class="card-title">实时处理</h5>
                <p class="card-text">毫秒级检测速度，支持批量处理，满足各种实时监控需求</p>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card h-100 text-center">
            <div class="card-body">
                <div class="mb-3">
                    <i class="fas fa-chart-bar fa-3x text-info"></i>
                </div>
                <h5 class="card-title">数据统计</h5>
                <p class="card-text">详细的检测报告和统计分析，帮助您了解脑肿瘤检测结果</p>
            </div>
        </div>
    </div>
</div>

<!-- 统计数据 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <h2 class="display-6">{{ stats.total_detections }}</h2>
                <p class="mb-0">总检测次数</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-danger text-white">
            <div class="card-body text-center">
                <h2 class="display-6">{{ stats.glioma_tumor_count }}</h2>
                <p class="mb-0">胶质瘤</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <h2 class="display-6">{{ stats.meningioma_tumor_count }}</h2>
                <p class="mb-0">脑膜瘤</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <h2 class="display-6">{{ stats.pituitary_tumor_count }}</h2>
                <p class="mb-0">垂体瘤</p>
            </div>
        </div>
    </div>
</div>

<!-- 数据可视化 -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-pie"></i> 检测结果分布
                </h5>
            </div>
            <div class="card-body">
                <canvas id="detectionChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-database"></i> 模型数据集信息
                </h5>
            </div>
            <div class="card-body">
                <canvas id="datasetChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- 最近检测情况 -->
{% if recent_records %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-clock"></i> 最近检测情况
                </h5>
                <a href="{% url 'detection:history' %}" class="btn btn-sm btn-outline-primary">
                    查看全部
                </a>
            </div>
            <div class="card-body">
                <div class="row">
                    {% for record in recent_records %}
                    <div class="col-md-6 col-lg-4 mb-3">
                        <div class="card border">
                            <div class="card-body">
                                <div class="d-flex align-items-center mb-2">
                                    <div class="flex-shrink-0">
                                        {% if record.result_image %}
                                            <img src="{{ record.result_image.url }}"
                                                 class="rounded" width="60" height="60"
                                                 style="object-fit: cover;">
                                        {% else %}
                                            <div class="bg-light rounded d-flex align-items-center justify-content-center"
                                                 style="width: 60px; height: 60px;">
                                                <i class="fas fa-image text-muted"></i>
                                            </div>
                                        {% endif %}
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <div class="fw-bold">{{ record.total_detections }} 个检测</div>
                                        <small class="text-muted">{{ record.upload_time|date:"m-d H:i" }}</small>
                                    </div>
                                </div>
                                <div class="row text-center">
                                    <div class="col-4">
                                        <small class="text-danger">{{ record.glioma_tumor_count }}</small>
                                        <br><small class="text-muted">胶质瘤</small>
                                    </div>
                                    <div class="col-4">
                                        <small class="text-warning">{{ record.meningioma_tumor_count }}</small>
                                        <br><small class="text-muted">脑膜瘤</small>
                                    </div>
                                    <div class="col-4">
                                        <small class="text-info">{{ record.pituitary_tumor_count }}</small>
                                        <br><small class="text-muted">垂体瘤</small>
                                    </div>
                                </div>
                                <div class="d-grid mt-2">
                                    <a href="{% url 'detection:detection_result' record.id %}"
                                       class="btn btn-sm btn-outline-primary">查看详情</a>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- 系统信息 -->
<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle"></i> 系统信息
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6">
                        <strong>AI模型:</strong><br>
                        <span class="text-muted">YOLO11</span>
                    </div>
                    <div class="col-6">
                        <strong>检测类别:</strong><br>
                        <span class="text-muted">3种</span>
                    </div>
                </div>
                <hr>
                <div class="row">
                    <div class="col-6">
                        <strong>Django版本:</strong><br>
                        <span class="text-muted">{{ django_version }}</span>
                    </div>
                    <div class="col-6">
                        <strong>Python版本:</strong><br>
                        <span class="text-muted">{{ python_version }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-tags"></i> 检测类别说明
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <span class="badge bg-danger me-2">🧠</span>
                    <strong>胶质瘤</strong>
                    <br><small class="text-muted">最常见的原发性脑肿瘤，起源于胶质细胞</small>
                </div>
                <div class="mb-3">
                    <span class="badge bg-warning me-2">🧠</span>
                    <strong>脑膜瘤</strong>
                    <br><small class="text-muted">起源于脑膜的肿瘤，通常为良性</small>
                </div>
                <div class="mb-3">
                    <span class="badge bg-info me-2">🧠</span>
                    <strong>垂体瘤</strong>
                    <br><small class="text-muted">发生在垂体的肿瘤，影响激素分泌</small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
$(document).ready(function() {
    // 检测结果分布饼状图
    const detectionCtx = document.getElementById('detectionChart').getContext('2d');
    new Chart(detectionCtx, {
        type: 'pie',
        data: {
            labels: ['胶质瘤', '脑膜瘤', '垂体瘤'],
            datasets: [{
                data: [{{ stats.glioma_tumor_count }}, {{ stats.meningioma_tumor_count }}, {{ stats.pituitary_tumor_count }}],
                backgroundColor: [
                    '#dc3545',
                    '#ffc107',
                    '#17a2b8'
                ],
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // 模型数据集信息饼状图（示例数据）
    const datasetCtx = document.getElementById('datasetChart').getContext('2d');
    new Chart(datasetCtx, {
        type: 'pie',
        data: {
            labels: ['训练集', '验证集', '测试集'],
            datasets: [{
                data: [70, 20, 10],
                backgroundColor: [
                    '#007bff',
                    '#17a2b8',
                    '#6c757d'
                ],
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
});
</script>
{% endblock %}
