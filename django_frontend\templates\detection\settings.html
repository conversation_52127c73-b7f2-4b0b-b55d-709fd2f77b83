{% extends 'base.html' %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <!-- 页面标题 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>
                <i class="fas fa-sliders-h"></i> 系统设置
            </h2>
            <a href="{% url 'detection:index' %}" class="btn btn-primary">
                <i class="fas fa-home"></i> 返回首页
            </a>
        </div>
    </div>
</div>

<div class="row">
    <!-- 默认检测参数设置 -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-cog"></i> 默认检测参数
                </h5>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.model_name.id_for_label }}" class="form-label">
                                    {{ form.model_name.label }}
                                </label>
                                {{ form.model_name }}
                                <div class="form-text">选择默认使用的检测模型</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.image_size.id_for_label }}" class="form-label">
                                    {{ form.image_size.label }}
                                </label>
                                {{ form.image_size }}
                                <div class="form-text">默认的图像处理尺寸</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.confidence_threshold.id_for_label }}" class="form-label">
                                    {{ form.confidence_threshold.label }}
                                </label>
                                {{ form.confidence_threshold }}
                                <div class="form-text">检测置信度阈值 (0.1-1.0)</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.iou_threshold.id_for_label }}" class="form-label">
                                    {{ form.iou_threshold.label }}
                                </label>
                                {{ form.iou_threshold }}
                                <div class="form-text">IOU重叠度阈值 (0.1-1.0)</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save"></i> 保存设置
                        </button>
                        <button type="reset" class="btn btn-outline-secondary">
                            <i class="fas fa-undo"></i> 重置
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- 系统信息 -->
    <div class="col-lg-4">
        <!-- 当前设置 -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-info-circle"></i> 当前设置
                </h6>
            </div>
            <div class="card-body">
                {% if request.session.default_params %}
                    <div class="mb-2">
                        <strong>模型:</strong> 
                        <span class="text-muted">{{ request.session.default_params.model_name|default:"未设置" }}</span>
                    </div>
                    <div class="mb-2">
                        <strong>置信度:</strong> 
                        <span class="text-muted">{{ request.session.default_params.confidence_threshold|default:"0.25" }}</span>
                    </div>
                    <div class="mb-2">
                        <strong>IOU阈值:</strong> 
                        <span class="text-muted">{{ request.session.default_params.iou_threshold|default:"0.45" }}</span>
                    </div>
                    <div class="mb-2">
                        <strong>图像尺寸:</strong> 
                        <span class="text-muted">{{ request.session.default_params.image_size|default:"640" }}x{{ request.session.default_params.image_size|default:"640" }}</span>
                    </div>
                {% else %}
                    <p class="text-muted">暂无自定义设置，使用系统默认值</p>
                {% endif %}
            </div>
        </div>
        
        <!-- 系统状态 -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-server"></i> 系统状态
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-2">
                    <strong>Django版本:</strong> 
                    <span class="text-muted">{{ django_version|default:"未知" }}</span>
                </div>
                <div class="mb-2">
                    <strong>Python版本:</strong> 
                    <span class="text-muted">{{ python_version|default:"未知" }}</span>
                </div>
                <div class="mb-2">
                    <strong>YOLO服务:</strong> 
                    <span class="badge bg-success">正常</span>
                </div>
                <div class="mb-2">
                    <strong>数据库:</strong> 
                    <span class="badge bg-success">连接正常</span>
                </div>
            </div>
        </div>
        
        <!-- 快捷操作 -->
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-tools"></i> 快捷操作
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{% url 'detection:model_management' %}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-cogs"></i> 模型管理
                    </a>
                    <a href="{% url 'detection:history' %}" class="btn btn-outline-info btn-sm">
                        <i class="fas fa-history"></i> 检测历史
                    </a>
                    <a href="/admin/" class="btn btn-outline-secondary btn-sm" target="_blank">
                        <i class="fas fa-user-shield"></i> 管理后台
                    </a>
                    <button class="btn btn-outline-warning btn-sm" onclick="clearCache()">
                        <i class="fas fa-broom"></i> 清理缓存
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 高级设置 -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-cogs"></i> 高级设置
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <h6>文件上传设置</h6>
                        <ul class="list-unstyled small text-muted">
                            <li><strong>最大文件大小:</strong> 10MB</li>
                            <li><strong>支持格式:</strong> JPG, PNG, BMP, JPEG</li>
                            <li><strong>存储路径:</strong> media/uploads/</li>
                        </ul>
                    </div>
                    <div class="col-md-4">
                        <h6>YOLO配置</h6>
                        <ul class="list-unstyled small text-muted">
                            <li><strong>模型目录:</strong> yoloserver/models/checkpoints/</li>
                            <li><strong>脚本目录:</strong> yoloserver/scripts/</li>
                            <li><strong>推理脚本:</strong> yolo_infer.py</li>
                        </ul>
                    </div>
                    <div class="col-md-4">
                        <h6>检测类别</h6>
                        <ul class="list-unstyled small text-muted">
                            <li><span class="badge bg-danger me-1">0</span> 胶质瘤</li>
                            <li><span class="badge bg-warning me-1">1</span> 脑膜瘤</li>
                            <li><span class="badge bg-info me-1">2</span> 垂体瘤</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 帮助信息 -->
<div class="row mt-4">
    <div class="col-12">
        <div class="alert alert-info">
            <h6><i class="fas fa-lightbulb"></i> 使用提示</h6>
            <ul class="mb-0 small">
                <li>默认参数设置会保存在浏览器会话中，关闭浏览器后会重置</li>
                <li>修改参数后，新的检测任务会使用更新后的默认值</li>
                <li>可以在检测页面临时调整参数，不会影响这里的默认设置</li>
                <li>建议根据实际使用场景调整置信度阈值，提高检测准确性</li>
            </ul>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function clearCache() {
    if (confirm('确定要清理缓存吗？这将清除所有保存的默认参数设置。')) {
        // 发送清理缓存请求
        fetch('/api/clear-cache/', {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('缓存清理成功！');
                location.reload();
            } else {
                alert('缓存清理失败: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('缓存清理失败');
        });
    }
}

// 表单验证
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const confidenceInput = document.getElementById('id_confidence_threshold');
    const iouInput = document.getElementById('id_iou_threshold');
    
    function validateRange(input, min, max) {
        const value = parseFloat(input.value);
        if (value < min || value > max) {
            input.classList.add('is-invalid');
            return false;
        } else {
            input.classList.remove('is-invalid');
            return true;
        }
    }
    
    if (confidenceInput) {
        confidenceInput.addEventListener('input', function() {
            validateRange(this, 0.1, 1.0);
        });
    }
    
    if (iouInput) {
        iouInput.addEventListener('input', function() {
            validateRange(this, 0.1, 1.0);
        });
    }
    
    if (form) {
        form.addEventListener('submit', function(e) {
            let valid = true;
            
            if (confidenceInput && !validateRange(confidenceInput, 0.1, 1.0)) {
                valid = false;
            }
            
            if (iouInput && !validateRange(iouInput, 0.1, 1.0)) {
                valid = false;
            }
            
            if (!valid) {
                e.preventDefault();
                alert('请检查输入的参数范围是否正确');
            }
        });
    }
});
</script>
{% endblock %}
