#!/usr/bin/env python
"""
测试标签文件解析功能
"""
import os
import sys
import django
from pathlib import Path

# 添加Django项目路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'django_frontend.settings')
django.setup()

from detection.services import InferenceService

def test_label_parsing():
    """测试标签文件解析"""
    print("🔍 开始测试标签文件解析...")
    
    # 测试标签文件路径
    label_file = Path("media/temp_inference/exp24/labels/G_208_jpg.rf.d0e841001a62522f5622cdf1c5a340ee.txt")
    
    if not label_file.exists():
        print(f"❌ 标签文件不存在: {label_file}")
        return False
    
    try:
        # 创建推理服务实例
        inference_service = InferenceService()
        
        # 解析标签文件
        detections = inference_service._parse_label_file(label_file)
        
        print(f"✅ 成功解析标签文件")
        print(f"📊 检测到 {len(detections)} 个目标")
        
        for i, detection in enumerate(detections, 1):
            print(f"\n目标 {i}:")
            print(f"  类别ID: {detection['class_id']}")
            print(f"  类别名称: {detection['class_name']}")
            print(f"  中心坐标: ({detection['x_center']:.4f}, {detection['y_center']:.4f})")
            print(f"  尺寸: {detection['width']:.4f} × {detection['height']:.4f}")
            print(f"  置信度: {detection['confidence']:.4f}")
            
            if 'polygon_points' in detection:
                print(f"  多边形点数: {len(detection['polygon_points'])}")
        
        return True
        
    except Exception as e:
        print(f"❌ 解析失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_label_parsing()
    if success:
        print("\n🎉 标签解析测试成功！")
    else:
        print("\n💥 标签解析测试失败！")
