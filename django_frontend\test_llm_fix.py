#!/usr/bin/env python
"""
测试LLM修复效果
"""
import os
import sys
import django
import json
from pathlib import Path

# 设置Django环境
sys.path.append(str(Path(__file__).parent))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'brain_tumor_detection.settings')
django.setup()

from detection.api_views import call_llm_api, build_analysis_context

def test_llm_with_retry():
    """测试带重试机制的LLM调用"""
    print("🔧 测试修复后的LLM功能...")
    
    # 模拟检测数据
    detection_data = {
        'total_detections': 2,
        'glioma_tumor_count': 1,
        'meningioma_tumor_count': 1,
        'pituitary_tumor_count': 0,
        'model_name': 'YOLOv8',
        'confidence_threshold': 0.5
    }
    
    prompt = "请分析这个脑肿瘤检测结果，给出专业的医学建议"
    
    try:
        print("构建分析上下文...")
        context = build_analysis_context(detection_data, prompt)
        print("✅ 分析上下文构建成功")
        
        print("调用LLM API（带重试机制）...")
        result = call_llm_api('Qwen/Qwen2.5-7B-Instruct', context)
        
        if result['success']:
            print("✅ LLM API调用成功!")
            print(f"使用模型: {result['model_used']}")
            print(f"API提供商: {result['api_provider']}")
            print(f"分析结果预览: {result['content'][:200]}...")
            return True
        else:
            print(f"❌ LLM API调用失败: {result['error']}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def test_different_models():
    """测试不同模型"""
    print("\n🤖 测试不同模型...")
    
    models = [
        'Qwen/Qwen2.5-7B-Instruct',
        'Qwen/Qwen2.5-14B-Instruct', 
        'Qwen/QwQ-32B',
        'deepseek-ai/DeepSeek-V2.5'
    ]
    
    detection_data = {
        'total_detections': 1,
        'glioma_tumor_count': 1,
        'meningioma_tumor_count': 0,
        'pituitary_tumor_count': 0
    }
    
    prompt = "简单分析一下这个检测结果"
    context = build_analysis_context(detection_data, prompt)
    
    results = []
    for model in models:
        print(f"\n测试模型: {model}")
        try:
            result = call_llm_api(model, context)
            if result['success']:
                print(f"  ✅ 成功 - {result['api_provider']}")
                results.append(True)
            else:
                print(f"  ❌ 失败 - {result['error']}")
                results.append(False)
        except Exception as e:
            print(f"  ❌ 异常 - {e}")
            results.append(False)
    
    success_rate = sum(results) / len(results) * 100
    print(f"\n模型测试成功率: {success_rate:.1f}% ({sum(results)}/{len(results)})")
    return success_rate > 50

def main():
    """主函数"""
    print("🔍 LLM修复效果测试")
    print("=" * 50)
    
    # 测试基本功能
    basic_test = test_llm_with_retry()
    
    # 测试不同模型
    model_test = test_different_models()
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 测试总结:")
    
    if basic_test and model_test:
        print("✅ 所有测试通过！LLM功能已修复")
        print("\n💡 使用建议:")
        print("1. 现在可以正常使用AI分析功能")
        print("2. 如果仍有超时，请耐心等待（最多2分钟）")
        print("3. 系统会自动重试3次，失败后提供备用分析")
    else:
        print("❌ 部分测试失败，可能仍有问题")
        print("\n🔧 进一步排查:")
        print("1. 检查网络连接稳定性")
        print("2. 确认API密钥有效性")
        print("3. 查看Django日志获取详细错误")

if __name__ == '__main__':
    main()
