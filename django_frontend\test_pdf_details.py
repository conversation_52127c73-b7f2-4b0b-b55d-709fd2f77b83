#!/usr/bin/env python
"""
测试PDF报告中的详细检测数据显示
"""
import os
import sys
import django
from pathlib import Path

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'django_frontend.settings')
django.setup()

from detection.models import DetectionRecord
from detection.pdf_service import PDFReportService

def test_pdf_details():
    """测试PDF详细检测数据"""
    try:
        # 获取最新记录
        record = DetectionRecord.objects.order_by('-id').first()
        if not record:
            print("❌ 没有找到检测记录")
            return False
        
        print(f"📋 测试记录: ID={record.id}, 患者={record.patient_name}")
        
        # 获取检测详情
        details = record.get_detection_details_json()
        print(f"📊 检测详情数量: {len(details)}")
        
        if details:
            for i, detail in enumerate(details, 1):
                print(f"\n检测 {i}:")
                print(f"  类别: {detail.get('class_name', '未知')}")
                print(f"  置信度: {detail.get('confidence', 0):.4f}")
                
                # 检查位置和尺寸数据
                if 'bbox' in detail:
                    bbox = detail['bbox']
                    print(f"  位置(bbox): ({bbox[0]:.4f}, {bbox[1]:.4f})")
                    print(f"  尺寸(bbox): ({bbox[2]:.4f}, {bbox[3]:.4f})")
                else:
                    x_center = detail.get('x_center', 0)
                    y_center = detail.get('y_center', 0)
                    width = detail.get('width', 0)
                    height = detail.get('height', 0)
                    
                    # 转换为左上角坐标
                    x_pos = x_center - width / 2
                    y_pos = y_center - height / 2
                    
                    print(f"  中心坐标: ({x_center:.4f}, {y_center:.4f})")
                    print(f"  尺寸: ({width:.4f}, {height:.4f})")
                    print(f"  左上角坐标: ({x_pos:.4f}, {y_pos:.4f})")
                    
                    # 转换为百分比显示
                    x_percent = x_pos * 100
                    y_percent = y_pos * 100
                    w_percent = width * 100
                    h_percent = height * 100
                    
                    print(f"  位置(百分比): ({x_percent:.1f}%, {y_percent:.1f}%)")
                    print(f"  尺寸(百分比): ({w_percent:.1f}%, {h_percent:.1f}%)")
        
        # 创建PDF服务并生成报告
        print(f"\n🔄 生成PDF报告...")
        pdf_service = PDFReportService()
        
        # 模拟LLM分析结果
        test_analysis = """
### AI分析结果

根据检测结果，发现了以下脑肿瘤：

1. **胶质瘤检测**
   - 检测到1个胶质瘤病灶
   - 置信度较高，建议进一步确认

### 建议
- 建议进行进一步的影像学检查
- 制定相应的治疗方案
        """
        
        pdf_data = pdf_service.create_pdf_report(record, test_analysis)
        
        # 保存PDF文件
        pdf_file = f"test_pdf_details_{record.id}.pdf"
        with open(pdf_file, 'wb') as f:
            f.write(pdf_data)
        
        print(f"✅ PDF报告生成成功: {pdf_file}")
        print(f"📄 文件大小: {len(pdf_data)} 字节")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=== PDF详细检测数据测试 ===\n")
    success = test_pdf_details()
    if success:
        print("\n🎉 测试成功！位置和尺寸信息应该正确显示在PDF中")
    else:
        print("\n💥 测试失败！")
