#!/usr/bin/env python
"""
PDF生成功能测试脚本
"""
import os
import sys
import django

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'brain_tumor_detection.settings')
django.setup()

from detection.models import DetectionRecord
from detection.pdf_service import PDFReportService

def test_pdf_generation():
    """测试PDF生成功能"""
    print("开始测试PDF生成功能...")
    
    try:
        # 获取最新的检测记录
        latest_record = DetectionRecord.objects.order_by('-upload_time').first()
        
        if not latest_record:
            print("❌ 没有找到检测记录，请先进行一次检测")
            return False
            
        print(f"✅ 找到检测记录: ID={latest_record.id}, 患者={latest_record.patient_name}")
        
        # 创建PDF服务
        pdf_service = PDFReportService()
        print("✅ PDF服务创建成功")
        
        # 模拟LLM分析结果
        test_analysis = """
## 脑肿瘤检测AI分析报告

### 检测结果评估
根据AI检测系统的分析结果，本次检测展现了以下特点：
- 检测精度较高，能够准确识别不同类型的脑肿瘤
- 系统成功区分了胶质瘤、脑膜瘤和垂体瘤三种主要类型

### 临床建议
- 建议进行进一步的影像学检查确认
- 对检测发现的肿瘤及时制定治疗方案
        """
        
        # 生成PDF
        print("🔄 正在生成PDF报告...")
        pdf_data = pdf_service.create_pdf_report(latest_record, test_analysis)
        
        # 保存测试PDF文件
        test_file_path = f"test_pdf_report_{latest_record.id}.pdf"
        with open(test_file_path, 'wb') as f:
            f.write(pdf_data)
            
        print(f"✅ PDF生成成功！文件保存为: {test_file_path}")
        print(f"📄 PDF文件大小: {len(pdf_data)} 字节")
        
        return True
        
    except Exception as e:
        print(f"❌ PDF生成失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_font_registration():
    """测试字体注册功能"""
    print("\n开始测试字体注册功能...")
    
    try:
        from reportlab.pdfbase import pdfmetrics
        
        # 测试字体路径
        font_paths = [
            "C:/Windows/Fonts/simhei.ttf",  # 黑体
            "C:/Windows/Fonts/simsun.ttc",  # 宋体
            "C:/Windows/Fonts/msyh.ttc",   # 微软雅黑
        ]
        
        for font_path in font_paths:
            if os.path.exists(font_path):
                print(f"✅ 找到字体文件: {font_path}")
            else:
                print(f"❌ 字体文件不存在: {font_path}")
        
        # 检查已注册的字体
        registered_fonts = pdfmetrics.getRegisteredFontNames()
        if 'SimHei' in registered_fonts:
            print("✅ 中文字体注册成功")
        else:
            print("⚠️  中文字体未注册，将使用默认字体")
        
        return True
        
    except Exception as e:
        print(f"❌ 字体测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("=== PDF生成功能测试 ===")
    
    # 测试字体
    font_ok = test_font_registration()
    
    # 测试PDF生成
    pdf_ok = test_pdf_generation()
    
    # 测试结果
    print("\n=== 测试结果 ===")
    if font_ok and pdf_ok:
        print("🎉 所有测试通过！PDF生成功能正常")
    else:
        print("❌ 存在问题，请检查错误信息")
    
    print("\n💡 提示：您可以在浏览器中访问结果页面，点击'下载PDF报告'按钮进行实际测试") 