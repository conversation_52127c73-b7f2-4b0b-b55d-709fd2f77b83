from pathlib import Path

def parse_label_file(label_file):
    """简单解析YOLO标签文件"""
    detections = []
    class_names = {
        0: 'glioma_tumor',
        1: 'meningioma_tumor',
        2: 'pituitary_tumor'
    }
    
    try:
        with open(label_file, 'r') as f:
            for line in f:
                parts = line.strip().split()
                if len(parts) < 2:
                    continue
                
                class_id = int(parts[0])
                class_name = class_names.get(class_id, 'unknown')
                
                # 分割格式：class_id x1 y1 x2 y2 ... [confidence]
                if len(parts) > 6:
                    coords = [float(x) for x in parts[1:]]
                    
                    # 检查是否有置信度
                    confidence = 0.0
                    if len(coords) % 2 == 1:
                        confidence = coords[-1]
                        coords = coords[:-1]
                    
                    # 确保坐标点数量是偶数
                    if len(coords) % 2 == 0 and len(coords) >= 6:
                        points = [(coords[i], coords[i+1]) for i in range(0, len(coords), 2)]
                        
                        # 计算边界框
                        x_coords = [p[0] for p in points]
                        y_coords = [p[1] for p in points]
                        
                        x_min, x_max = min(x_coords), max(x_coords)
                        y_min, y_max = min(y_coords), max(y_coords)
                        
                        x_center = (x_min + x_max) / 2
                        y_center = (y_min + y_max) / 2
                        width = x_max - x_min
                        height = y_max - y_min
                        
                        detection = {
                            'class_id': class_id,
                            'class_name': class_name,
                            'x_center': x_center,
                            'y_center': y_center,
                            'width': width,
                            'height': height,
                            'confidence': confidence,
                            'polygon_points': len(points)
                        }
                        detections.append(detection)
                    
    except Exception as e:
        print(f"Error parsing {label_file}: {str(e)}")
    
    return detections

# 测试
label_file = Path("media/temp_inference/exp24/labels/G_208_jpg.rf.d0e841001a62522f5622cdf1c5a340ee.txt")
if label_file.exists():
    detections = parse_label_file(label_file)
    print(f"Found {len(detections)} detections:")
    for i, det in enumerate(detections, 1):
        print(f"Detection {i}:")
        print(f"  Class: {det['class_name']}")
        print(f"  Center: ({det['x_center']:.4f}, {det['y_center']:.4f})")
        print(f"  Size: {det['width']:.4f} x {det['height']:.4f}")
        print(f"  Confidence: {det['confidence']:.4f}")
        print(f"  Polygon points: {det['polygon_points']}")
else:
    print(f"Label file not found: {label_file}")
