import os
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'django_frontend.settings')
django.setup()

from detection.templatetags.detection_filters import *

# Test data
test_detection = {
    'class_id': 0,
    'class_name': 'glioma_tumor',
    'x_center': 0.6586,
    'y_center': 0.3398,
    'width': 0.1859,
    'height': 0.1984,
    'confidence': 0.9325
}

print("Testing template filters:")
print(f"Center X: {get_center_x(test_detection)}")
print(f"Center Y: {get_center_y(test_detection)}")
print(f"Width: {get_width(test_detection)}")
print(f"Height: {get_height(test_detection)}")
print(f"Position X: {get_position_x(test_detection)}")
print(f"Position Y: {get_position_y(test_detection)}")
print(f"Formatted center X: {format_coordinate(get_center_x(test_detection))}")
print(f"Formatted position X: {format_coordinate(get_position_x(test_detection))}")

print("\nExpected display in template:")
print(f"Center: ({format_coordinate(get_center_x(test_detection))}, {format_coordinate(get_center_y(test_detection))})")
print(f"Position: ({format_coordinate(get_position_x(test_detection))}, {format_coordinate(get_position_y(test_detection))})")
print(f"Size: ({format_coordinate(get_width(test_detection))}, {format_coordinate(get_height(test_detection))})")
