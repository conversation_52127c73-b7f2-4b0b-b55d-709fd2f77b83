"""
测试上传表单功能
"""
import os
import django
import sys
from io import BytesIO
from PIL import Image

# 添加Django项目路径到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'brain_tumor_detection.settings')
django.setup()

from django.test.client import Client
from django.core.files.uploadedfile import SimpleUploadedFile
from django.urls import reverse


def create_test_image():
    """创建一个测试图像"""
    # 创建一个RGB图像
    img = Image.new('RGB', (640, 640), color='red')
    
    # 将图像保存到BytesIO对象
    img_io = BytesIO()
    img.save(img_io, format='JPEG')
    img_io.seek(0)
    
    return img_io.getvalue()


def test_upload_form():
    """测试上传表单"""
    try:
        print("🧪 开始测试上传表单...")
        
        # 创建测试客户端
        client = Client()
        
        # 首先测试检测页面是否可以正常访问
        print("1. 测试检测页面访问...")
        response = client.get('/detect/')
        print(f"   检测页面状态码: {response.status_code}")
        
        if response.status_code != 200:
            print(f"❌ 检测页面无法访问，状态码: {response.status_code}")
            return False
        
        # 创建测试图像
        print("2. 创建测试图像...")
        test_image_data = create_test_image()
        
        # 创建上传文件对象
        uploaded_file = SimpleUploadedFile(
            "test_brain_image.jpg",
            test_image_data,
            content_type="image/jpeg"
        )
        
        # 准备表单数据
        form_data = {
            'patient_name': '测试患者',
            'patient_gender': 'male',
            'patient_age': 45,
            'confidence_threshold': 0.25,
            'iou_threshold': 0.45,
            'image_size': 640,
            'model_name': 'train2_20250626-164018_yolo11m-seg_best.pt',  # 使用已存在的模型
            'original_image': uploaded_file
        }
        
        print("3. 提交表单数据...")
        print(f"   患者姓名: {form_data['patient_name']}")
        print(f"   患者性别: {form_data['patient_gender']}")
        print(f"   患者年龄: {form_data['patient_age']}")
        print(f"   检测模型: {form_data['model_name']}")
        
        # 提交表单
        response = client.post('/upload/', form_data, follow=True)
        
        print(f"   表单提交状态码: {response.status_code}")
        print(f"   最终URL: {response.request['PATH_INFO']}")
        
        # 检查是否成功重定向到结果页面
        if response.status_code == 200:
            if 'result' in response.request['PATH_INFO']:
                print("✅ 表单提交成功，正确重定向到结果页面")
                return True
            elif 'detect' in response.request['PATH_INFO'] or response.request['PATH_INFO'] == '/':
                print("⚠️  表单提交后重定向回检测页面或首页")
                # 检查响应内容中是否有错误信息
                content = response.content.decode('utf-8')
                if 'alert' in content or 'error' in content.lower():
                    print("   页面中可能包含错误信息")
                return False
            else:
                print(f"🤔 意外的重定向目标: {response.request['PATH_INFO']}")
                return False
        else:
            print(f"❌ 表单提交失败，状态码: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_url_patterns():
    """测试URL模式"""
    print("\n🔗 测试URL模式...")
    
    try:
        # 测试主要URL是否可以正确解析
        urls_to_test = [
            ('detection:index', '/'),
            ('detection:detect', '/detect/'),
            ('detection:upload_and_detect', '/upload/'),
            ('detection:history', '/history/'),
        ]
        
        for url_name, expected_path in urls_to_test:
            try:
                actual_path = reverse(url_name)
                if actual_path == expected_path:
                    print(f"✅ {url_name}: {actual_path}")
                else:
                    print(f"⚠️  {url_name}: 期望 {expected_path}, 实际 {actual_path}")
            except Exception as e:
                print(f"❌ {url_name}: 无法解析 - {str(e)}")
                
        return True
        
    except Exception as e:
        print(f"❌ URL测试失败: {str(e)}")
        return False


if __name__ == "__main__":
    print("🚀 开始表单上传测试...")
    print("=" * 60)
    
    # 测试URL模式
    url_test_success = test_url_patterns()
    
    # 测试表单上传
    form_test_success = test_upload_form()
    
    print("=" * 60)
    if url_test_success and form_test_success:
        print("✅ 所有测试通过！表单上传功能正常工作")
    else:
        print("❌ 测试失败！需要进一步检查问题")
        
    print("\n💡 如果测试失败，请检查:")
    print("   1. Django服务器是否正在运行")
    print("   2. 模型文件是否存在于指定路径")
    print("   3. 数据库是否正确迁移")
    print("   4. URL配置是否正确") 