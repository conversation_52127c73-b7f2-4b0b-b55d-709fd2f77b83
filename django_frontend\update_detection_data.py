#!/usr/bin/env python
"""
更新检测记录的数据，重新解析标签文件
"""
import os
import sys
import django
from pathlib import Path

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'django_frontend.settings')
django.setup()

from detection.models import DetectionRecord
from detection.services import InferenceService

def update_detection_record(record_id):
    """更新指定记录的检测数据"""
    try:
        record = DetectionRecord.objects.get(id=record_id)
        print(f"正在更新记录 ID: {record_id}, 患者: {record.patient_name}")
        
        # 查找对应的标签文件
        # 根据推理结果目录结构查找
        media_dir = Path("media/temp_inference")
        labels_dirs = list(media_dir.glob("exp*/labels"))
        
        if not labels_dirs:
            print("❌ 未找到标签目录")
            return False
        
        # 使用最新的标签目录
        latest_labels_dir = max(labels_dirs, key=lambda x: x.stat().st_mtime)
        print(f"使用标签目录: {latest_labels_dir}")
        
        # 查找标签文件
        label_files = list(latest_labels_dir.glob("*.txt"))
        if not label_files:
            print("❌ 标签目录中没有找到标签文件")
            return False
        
        print(f"找到 {len(label_files)} 个标签文件")
        
        # 创建推理服务实例
        inference_service = InferenceService()
        
        # 重新解析所有标签文件
        all_detections = []
        for label_file in label_files:
            detections = inference_service._parse_label_file(label_file)
            all_detections.extend(detections)
            print(f"解析 {label_file.name}: {len(detections)} 个检测")
        
        if not all_detections:
            print("❌ 没有解析到任何检测数据")
            return False
        
        # 统计各类别数量
        glioma_count = sum(1 for d in all_detections if d['class_id'] == 0)
        meningioma_count = sum(1 for d in all_detections if d['class_id'] == 1)
        pituitary_count = sum(1 for d in all_detections if d['class_id'] == 2)
        
        # 更新记录
        record.detection_details = all_detections
        record.total_detections = len(all_detections)
        record.glioma_tumor_count = glioma_count
        record.meningioma_tumor_count = meningioma_count
        record.pituitary_tumor_count = pituitary_count
        record.save()
        
        print(f"✅ 记录更新成功:")
        print(f"  总检测数: {record.total_detections}")
        print(f"  胶质瘤: {record.glioma_tumor_count}")
        print(f"  脑膜瘤: {record.meningioma_tumor_count}")
        print(f"  垂体瘤: {record.pituitary_tumor_count}")
        
        # 显示第一个检测的详细信息
        if all_detections:
            first_detection = all_detections[0]
            print(f"\n第一个检测的详细信息:")
            print(f"  类别: {first_detection['class_name']}")
            print(f"  中心坐标: ({first_detection['x_center']:.4f}, {first_detection['y_center']:.4f})")
            print(f"  尺寸: {first_detection['width']:.4f} × {first_detection['height']:.4f}")
            print(f"  置信度: {first_detection['confidence']:.4f}")
        
        return True
        
    except DetectionRecord.DoesNotExist:
        print(f"❌ 记录 ID {record_id} 不存在")
        return False
    except Exception as e:
        print(f"❌ 更新失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # 更新最新的记录
    latest_record = DetectionRecord.objects.order_by('-id').first()
    if latest_record:
        print(f"更新最新记录 ID: {latest_record.id}")
        success = update_detection_record(latest_record.id)
        if success:
            print("\n🎉 数据更新成功！")
        else:
            print("\n💥 数据更新失败！")
    else:
        print("❌ 没有找到任何检测记录")
