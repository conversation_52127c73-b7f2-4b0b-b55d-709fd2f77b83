#!/usr/bin/env python
"""
验证口罩检测代码清理完成情况
"""
import os
import re
from pathlib import Path

def search_files_for_patterns(directory, patterns, extensions):
    """在指定目录中搜索包含特定模式的文件"""
    results = []
    
    for root, dirs, files in os.walk(directory):
        # 跳过一些不需要检查的目录
        dirs[:] = [d for d in dirs if d not in ['.git', '__pycache__', 'node_modules', '.venv']]
        
        for file in files:
            if any(file.endswith(ext) for ext in extensions):
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                        
                    for pattern in patterns:
                        matches = re.finditer(pattern, content, re.IGNORECASE)
                        for match in matches:
                            line_num = content[:match.start()].count('\n') + 1
                            line_content = content.split('\n')[line_num - 1].strip()
                            results.append({
                                'file': file_path,
                                'line': line_num,
                                'pattern': pattern,
                                'content': line_content
                            })
                except Exception as e:
                    print(f"无法读取文件 {file_path}: {e}")
    
    return results

def main():
    """主函数"""
    print("🔍 验证口罩检测代码清理情况")
    print("=" * 60)
    
    # 定义搜索模式
    mask_patterns = [
        r'口罩',
        r'mask',
        r'合规率',
        r'compliance',
        r'with_mask',
        r'without_mask',
        r'incorrect_mask',
        r'mask_weared_incorrect',
        r'正确戴口罩',
        r'未戴口罩',
        r'错误戴口罩',
        r'佩戴情况',
        r'佩戴口罩'
    ]
    
    # 定义要检查的文件扩展名
    extensions = ['.py', '.html', '.js', '.md', '.txt']
    
    # 检查django_frontend目录
    django_dir = Path(__file__).parent
    print(f"检查目录: {django_dir}")
    
    results = search_files_for_patterns(django_dir, mask_patterns, extensions)
    
    if results:
        print(f"\n❌ 发现 {len(results)} 处需要清理的代码:")
        print("-" * 60)
        
        current_file = None
        for result in results:
            if result['file'] != current_file:
                current_file = result['file']
                rel_path = os.path.relpath(result['file'], django_dir)
                print(f"\n📁 文件: {rel_path}")
            
            print(f"   第{result['line']}行: {result['content']}")
            print(f"   匹配模式: {result['pattern']}")
        
        print("\n" + "=" * 60)
        print("🔧 需要手动清理以上代码")
        
    else:
        print("✅ 未发现口罩检测相关代码残留")
        print("\n🎉 代码清理验证通过！")
    
    # 检查LLM目录
    llm_dir = django_dir.parent / 'LLM'
    if llm_dir.exists():
        print(f"\n检查LLM目录: {llm_dir}")
        llm_results = search_files_for_patterns(llm_dir, mask_patterns, extensions)
        
        if llm_results:
            print(f"\n❌ LLM目录发现 {len(llm_results)} 处需要清理的代码:")
            print("-" * 60)
            
            current_file = None
            for result in llm_results:
                if result['file'] != current_file:
                    current_file = result['file']
                    rel_path = os.path.relpath(result['file'], llm_dir)
                    print(f"\n📁 文件: {rel_path}")
                
                print(f"   第{result['line']}行: {result['content']}")
                print(f"   匹配模式: {result['pattern']}")
        else:
            print("✅ LLM目录未发现口罩检测相关代码残留")
    
    # 总结
    total_issues = len(results) + (len(llm_results) if 'llm_results' in locals() else 0)
    
    print("\n" + "=" * 60)
    print("📊 清理验证总结:")
    
    if total_issues == 0:
        print("✅ 所有口罩检测相关代码已清理完成")
        print("🎯 系统已完全适配脑肿瘤检测")
        print("\n💡 建议:")
        print("1. 重新启动Django服务器")
        print("2. 测试所有功能确保正常工作")
        print("3. 检查前端显示是否符合脑肿瘤检测主题")
    else:
        print(f"⚠️  发现 {total_issues} 处需要清理的代码")
        print("🔧 请根据上述信息手动清理剩余代码")
    
    return total_issues == 0

if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)
