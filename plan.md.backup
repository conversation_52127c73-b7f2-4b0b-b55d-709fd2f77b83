1.路径管理模块设计与实现
2.开发日志工具

常见的数据格式
Pascal Voc格式，每一个图像文件，对应一个XML注释文件，包含图片路径、尺寸、每个目标的详细信息、边界框信息，记录类别名称和边界框坐标
COCO json格式，结构复杂，功能强大，包含丰富的标注类型，支持边界框标注还支持实例分割多边形轮廓。
yolo txt格式，算法组采用的数据

图像数据：
    主流的图像格式：PNG、JPG
    图像应该清晰显示需要检测和分割单肿瘤或病变区域
标注数据：
    目标检测任务：需要边界信息
    实例分割任务：需要边界信息和分割信息

数据转换核心开发任务需求文档
    1.将pascal voc、 COCO-json转换为yolo-txt
    ·关键点：支持coco-json到yolo-txt的转换；
             支持pascal-voc到yolo-txt的转换；
             能够自动提取数据集中的所有类别，也可以按照用户指定的类别进行过滤和ID映射；
             具备良好的错误处理机制；
             需要自动生成data.yaml配置文件，到指定的位置configs目录下：
             path:
             train:
             val:
             test:
             nc:
             names:
    ·如何设计
        最底层：不同的格式有不同的转换工具
        一个统一的入口：中间转换层。接受输入的目录、输出的目录，原始标注格式类型和可选的类别作为参数。
        最顶层：将转换之后的yolo txt数据划分为训练集、验证集和测试集，图像和标签均位与设定好的位置，并且要求生成对应的data.yaml文件
        需要保证每个数据集的标签是一致的
    2.数据集分割
    3.生成data.yaml文件
    4.日志记录

中间层data_utils.py

顶层脚本yolo_trans.py放置到scripts目录里面

支持命令行接口（CIL）做解析
调用data_utils实现转换
数据集的组织与分割，将灰分后的图像和标签按照指定比例放置到train/images，train/labels，test/images，test/labels，等一共六个
还需要能够自动生成data.yaml文件，内容如下，必须覆盖输出
目录清理与初始化，每次运行前，可选的清理之前的划分目录和data.yaml文件，确保每次运行都是干净的状态
全面的日志记录
性能计时


数据验证
保证数据质量和一致性
    格式正确性：检测任务有5列值，坐标范围都在0-1之间
    分割任务：1+N*2个值，其中坐标至少要大于3组
类别ID有效性：验证标签类别ID是否在data.yaml中定义的有效范围，防止出现越界ID
图像-标签匹配问题，确认每张图片都有对应的标签

避免数据泄露：
    保证数据分割唯一性，确保训练集、测试集、验证集没有重叠，数据泄露是模型评估中非常严重的错误。

核心结构划分：
    utils/data_validation.py：实现数据验证逻辑模块
    ·保证数据质量和一致性
    ·避免数据泄露
    scripts/yolo_validate.py：实现数据验证的顶层脚本
    ·随机抽取其中一部分进行数据验证，支持全量检测和抽样检测
    ·对于不合法的数据、信息做一个输出，然后输出异常数据数量，让用户自行决定是否删除异常数据

模型训练需要支持的功能
    1.日志功能
    2.每次训练，都会生成递增目录，模型在weights文件夹中，一般有两，一个best一个last，用户选择模型
    时，很不方便，因此考虑将所有训练好的模型放到指定的yoloserver/models/checkpoints，然后模型的
    名称，命名为:trainN_年月日-时分秒_yolo11m_seg_best/last.pt
    3.用户选择预训练模型，在yoloserver/models/pretrained去找
    4.用户训练时，参数设定，至少让他支持CIL命令，支持yaml配置文件中的参数，命令行没有设置的参数，
    则使用yaml配置文件中的参数，用户训练过程中，所有的参数记录到日志当中，参数来源应当有所区分.
    必然涉及参数合并，参数来源标记，参数优先级，参数yaml文件的生成
    5.推理结果的保存和显示，推理结果需要记录到日志当中。
    6.训练过程中使用的数据信息，记录到日志
    7.
    
模型训练：
    1.日志功能，已经实现
    2.日志重命名：常规的是当前动作＋当前时间。对于模型训练，日志名称是TrainN+当前的时间+模
    型名.log；对于模型验证，日志名称是valN+当前时间+使用的模型名称.log
    3.加载yaml配置中的模型参数，生成默认的yaml配置文件
    4.参数合并：参数优先级实现CIL>YAML>默认， 用于模型训练的yolo_args，project_args
    5.设备信息：记录用户训练时使用的设备信息，用的设备是什么，核心模块的版本信息，CPU和GPU使用率
    6.数据信息：记录数据集信息，类别数，样本数等等
    7.训练结果的信息：记录训练结果的信息，3-4终速度信息
    8.训练参数信息：哪些来自CIL，哪些来自yaml配置文件
    9.模型拷贝：拷贝训练好的模型到模型存放点，便于管理和复现
    10.主训练脚本：整合训练逻辑，错误处理，进度反馈，提升健壮性


test_rename_log_file:
#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试 rename_log_file 函数的脚本
"""

import sys
from pathlib import Path
import time

# 添加项目根目录到 Python 路径
project_root = Path(__file__).resolve().parent
sys.path.insert(0, str(project_root))

from yoloserver.utils import setup_logger, rename_log_file, LOGS_DIR

def test_rename_log_file():
    """测试日志文件重命名功能"""
    print("=== 测试 rename_log_file 函数 ===")
    
    # 1. 创建一个临时日志记录器
    print("\n1. 创建临时日志记录器...")
    logger = setup_logger(
        base_path=LOGS_DIR,
        log_type="test_rename",
        model_name="yolov8n",
        temp_log=True,  # 使用临时文件名
        logger_name="RenameTest"
    )
    
    # 2. 写入一些测试日志
    print("2. 写入测试日志...")
    logger.info("这是重命名前的测试日志")
    logger.info("模拟训练开始...")
    logger.info("模拟训练进行中...")
    
    # 3. 模拟获取保存目录（如训练结果目录）
    save_dir = "runs/train/train1"  # 模拟训练输出目录
    model_name = "yolov8n"
    
    print(f"3. 准备重命名日志文件...")
    print(f"   保存目录: {save_dir}")
    print(f"   模型名称: {model_name}")
    
    # 4. 执行重命名操作
    print("4. 执行日志文件重命名...")
    rename_log_file(logger, save_dir, model_name)
    
    # 5. 写入重命名后的日志
    print("5. 写入重命名后的日志...")
    logger.info("这是重命名后的测试日志")
    logger.info("模拟训练完成")
    logger.info("日志文件重命名测试成功")
    
    print("\n✅ 测试完成！请检查 yoloserver/logs/test_rename/ 目录下的日志文件")

def test_edge_cases():
    """测试边缘情况"""
    print("\n=== 测试边缘情况 ===")
    
    # 测试1：无效的日志记录器
    print("\n1. 测试无效的日志记录器...")
    try:
        rename_log_file(None, "test_dir", "test_model")
        print("❌ 应该产生警告")
    except Exception as e:
        print(f"✅ 正确处理了无效输入: {e}")
    
    # 测试2：空的保存目录
    print("\n2. 测试空的保存目录...")
    logger2 = setup_logger(
        base_path=LOGS_DIR,
        log_type="test_edge",
        model_name="test",
        temp_log=True,
        logger_name="EdgeTest"
    )
    
    logger2.info("测试空保存目录")
    rename_log_file(logger2, "", "test_model")
    logger2.info("空保存目录测试完成")
    
    # 测试3：重复重命名
    print("\n3. 测试重复重命名...")
    logger3 = setup_logger(
        base_path=LOGS_DIR,
        log_type="test_duplicate",
        model_name="duplicate",
        temp_log=True,
        logger_name="DuplicateTest"
    )
    
    logger3.info("第一次重命名前")
    rename_log_file(logger3, "runs/train/exp1", "yolov8n")
    logger3.info("第一次重命名后")
    
    # 再次重命名到相同目录
    rename_log_file(logger3, "runs/train/exp1", "yolov8n")
    logger3.info("第二次重命名后")
    
    print("\n✅ 边缘情况测试完成")

if __name__ == "__main__":
    test_rename_log_file()
    test_edge_cases()
    print("\n🎉 所有测试完成！")