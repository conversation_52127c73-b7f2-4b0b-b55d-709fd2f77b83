# 项目初始化脚本使用说明

## 概述

`init_project.py` 是医学图像检测与分割系统的项目初始化脚本，用于自动创建完整的项目目录结构。

## 功能特性

### 核心功能
- **自动目录创建**: 根据项目需求自动创建所有必要的目录结构
- **Git兼容性**: 为空目录自动添加空的 `.keep` 文件，确保能被Git正确跟踪
- **智能检测**: 自动检测已存在的目录，避免重复创建
- **详细日志**: 提供完整的操作日志和执行反馈

### 监控与反馈
- **性能监控**: 自动测量脚本执行时间和各功能模块耗时
- **统计报告**: 详细统计创建、已存在和失败的目录数量
- **用户指引**: 提供清晰的后续操作指导

### 错误处理
- **异常捕获**: 完善的错误处理机制
- **失败记录**: 记录所有失败的操作以便排查
- **安全执行**: 不会覆盖已存在的文件和目录

## 使用方法

### 基本使用

```bash
# 进入yoloserver目录
cd yoloserver

# 运行初始化脚本
python init_project.py
```

### 运行要求

- Python 3.6+
- 确保 `utils/logging_utils.py` 和 `utils/performance_utils.py` 文件存在

## 创建的目录结构

### 配置文件目录
```
configs/
├── models/          # 模型配置
├── api/            # API配置
└── deployment/     # 部署配置
```

### 模型相关目录
```
models/
├── checkpoints/    # 训练检查点
├── pretrained/     # 预训练模型
├── onnx/          # ONNX优化模型
└── tensorrt/      # TensorRT优化模型
```

### 数据管理目录
```
data/
├── raw/                    # 原始数据
│   ├── images/            # 原始医学影像
│   ├── original_annotations/  # 原始标注文件
│   ├── yolo/              # YOLO格式标注
│   └── coco/              # COCO格式标注
├── processed/             # 处理后数据
├── temp/                  # 临时数据
├── train/                 # 训练集
│   ├── images/
│   └── labels/
├── val/                   # 验证集
│   ├── images/
│   └── labels/
└── test/                  # 测试集
    ├── images/
    └── labels/
```

### 运行结果目录
```
runs/
├── train/          # 训练结果
├── detect/         # 检测结果
├── segment/        # 分割结果
└── export/         # 模型导出结果
```

### 日志和监控目录
```
logs/
├── app_log/        # 应用日志
├── models_log/     # 模型日志
├── api_log/        # API日志
├── errors_log/     # 错误日志
├── inference_log/  # 推理日志
├── training_log/   # 训练日志
├── init_log/       # 初始化日志
└── test_log/       # 测试日志
```

### 脚本和工具目录
```
scripts/
├── training/       # 训练脚本
├── inference/      # 推理脚本
├── data_processing/  # 数据处理脚本
└── deployment/     # 部署脚本
```

### 其他目录
```
docs/               # 文档目录
tests/              # 测试目录
```

## 用户操作指引

### 需要手动添加内容的目录

1. **`data/raw/images/`** - 请将原始医学影像文件放置在此目录
2. **`data/raw/original_annotations/`** - 请将原始标注文件放置在此目录
3. **`models/pretrained/`** - 请下载并放置预训练模型文件
4. **`configs/`** - 请根据需要添加配置文件

### 自动管理的目录（无需手动操作）

- **`runs/`** - 训练和推理结果将自动保存在此
- **`logs/`** - 所有日志文件将自动生成
- **`data/processed/`** - 处理后的数据将自动保存
- **`data/temp/`** - 临时文件将自动管理

### 建议的下一步操作

1. 将医学影像数据复制到 `data/raw/images/` 目录
2. 将标注文件复制到 `data/raw/original_annotations/` 目录
3. 下载所需的预训练模型到 `models/pretrained/` 目录
4. 根据项目需求配置 `configs/` 目录下的配置文件
5. 运行数据预处理脚本准备训练数据

## 重要提醒

**注意事项**:
- 请勿删除 `.keep` 文件，它们确保空目录能被Git正确跟踪
- `logs/` 目录下的文件会自动生成，无需手动创建
- 如需重新初始化，可以重新运行此脚本
- 脚本是幂等的，多次运行不会产生副作用

## 日志文件

初始化过程中的所有操作都会记录在日志文件中：
- 日志位置: `logs/init_log/temp_YYYYMMDD-HHMMSS_project_init.log`
- 日志包含: 详细的操作记录、性能统计、错误信息等

## 故障排除

如果遇到问题，请检查：
1. Python版本是否符合要求
2. 是否有足够的磁盘空间
3. 是否有相应的文件系统权限
4. 查看日志文件了解详细错误信息

---