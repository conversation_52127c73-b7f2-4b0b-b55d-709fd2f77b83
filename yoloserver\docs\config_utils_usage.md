# 配置管理模块使用指南

## 概述

`config_utils.py` 模块提供了完整的 YAML 配置文件管理功能，支持医学图像检测与分割系统的各种配置需求。该模块实现了 FR-002 和 FR-003 功能需求，确保配置参数的外部化、可追溯性和健壮性。

## 主要功能

### FR-002: 加载 YAML 配置 (load_yaml_config)
- 自动加载指定类型的配置文件
- 支持自定义配置文件路径
- 配置文件不存在时自动生成默认配置
- 完整的异常处理和日志记录

### FR-003: 生成默认 YAML 配置 (generate_default_config)
- 生成带注释的默认配置文件
- 支持多种配置类型：train, val, infer, data
- 自动创建目录结构
- 健壮的错误处理

## 支持的配置类型

| 配置类型 | 文件名 | 用途 | 主要参数 |
|---------|--------|------|----------|
| `train` | `train.yaml` | 模型训练 | 模型配置、训练参数、数据增强、优化器设置 |
| `val` | `val.yaml` | 模型验证 | 验证参数、输出设置、设备配置 |
| `infer` | `infer.yaml` | 模型推理 | 推理参数、输出配置、类别过滤 |
| `data` | `data.yaml` | 数据集配置 | 数据路径、类别信息、基础训练参数 |

## 基本使用方法

### 1. 导入模块

```python
from utils import (
    load_yaml_config,
    generate_default_config,
    merge_configs,
    validate_config
)
```

### 2. 加载配置文件

```python
# 加载默认路径的配置文件
train_config = load_yaml_config('train')
data_config = load_yaml_config('data')

# 加载自定义路径的配置文件
custom_config = load_yaml_config('train', 'custom/my_train.yaml')
```

### 3. 生成默认配置

```python
# 生成默认训练配置到标准路径
generate_default_config('train')

# 生成到自定义路径
generate_default_config('infer', 'custom/my_infer.yaml')
```

### 4. 合并配置

```python
# 合并多个配置文件
base_config = load_yaml_config('data')
train_config = load_yaml_config('train')
final_config = merge_configs(base_config, train_config)
```

### 5. 验证配置

```python
# 验证配置完整性
required_keys = ['model', 'training', 'data']
is_valid = validate_config(final_config, required_keys)
```

## 实际应用示例

### 训练脚本配置

```python
import logging
from utils import setup_logger, load_yaml_config, merge_configs, validate_config

# 设置日志
logger = setup_logger(
    base_path=LOGS_DIR,
    log_type="training",
    model_name="yolov8n"
)

try:
    # 加载配置
    data_config = load_yaml_config('data')
    train_config = load_yaml_config('train')
    
    # 合并配置
    config = merge_configs(data_config, train_config)
    
    # 验证配置
    required_keys = ['nc', 'names', 'model', 'training']
    if validate_config(config, required_keys):
        logger.info("配置验证通过，开始训练...")
        
        # 获取训练参数
        model_name = config['model']['name']
        epochs = config['training']['epochs']
        batch_size = config['training']['batch_size']
        
        # 开始训练...
        
    else:
        logger.error("配置验证失败")
        
except Exception as e:
    logger.error(f"配置加载失败: {e}")
```

### 推理脚本配置

```python
# 加载推理配置
infer_config = load_yaml_config('infer')
data_config = load_yaml_config('data')

# 合并配置
config = merge_configs(data_config, infer_config)

# 获取推理参数
weights = config['model']['weights']
conf_thres = config['inference']['conf_thres']
source = config['data']['source']

# 开始推理...
```

## 配置文件结构

### 训练配置 (train.yaml)

```yaml
# 模型配置
model:
  name: "yolov8n"
  pretrained: true

# 训练参数
training:
  epochs: 100
  batch_size: 16
  learning_rate: 0.01
  patience: 50

# 数据增强
augmentation:
  hsv_h: 0.015
  hsv_s: 0.7
  # ... 更多参数

# 优化器配置
optimizer:
  name: "SGD"
  momentum: 0.937
  weight_decay: 0.0005
```

### 数据配置 (data.yaml)

```yaml
# 数据集路径
path: "data"
train: "data/train/images"
val: "data/val/images"
test: "data/test/images"

# 类别配置
nc: 3
names: 
  - "glioma_tumor"
  - "meningioma_tumor"
  - "pituitary_tumor"

# 基础训练参数
epochs: 100
batch: 16
```

## 错误处理

模块提供完整的错误处理机制：

1. **不支持的配置类型**: 抛出 `ValueError`
2. **文件不存在**: 自动生成默认配置或抛出 `FileNotFoundError`
3. **YAML 解析错误**: 抛出 `yaml.YAMLError`
4. **文件写入失败**: 抛出 `IOError`

## 辅助功能

### 获取配置信息

```python
from utils import get_config_info, list_supported_configs

# 获取单个配置信息
info = get_config_info('train')
print(f"配置类型: {info['config_type']}")
print(f"默认路径: {info['default_path']}")
print(f"文件存在: {info['exists']}")

# 列出所有支持的配置
all_configs = list_supported_configs()
for config_type, info in all_configs.items():
    print(f"{config_type}: {info['description']}")
```

## 最佳实践

1. **使用标准配置类型**: 优先使用预定义的配置类型 (train, val, infer, data)
2. **配置验证**: 在使用配置前进行验证，确保包含必需的键
3. **日志记录**: 配合项目的日志系统使用，便于调试和审计
4. **配置合并**: 使用 `merge_configs` 组合多个配置文件
5. **异常处理**: 妥善处理配置加载过程中的异常

## 注意事项

- 配置文件使用 UTF-8 编码
- YAML 文件支持注释，便于理解和维护
- 后加载的配置会覆盖先加载的同名键值
- 自动生成的配置文件包含详细的参数说明
- 所有操作都有完整的日志记录

## 相关文件

- `yoloserver/utils/config_utils.py` - 主要实现文件
- `yoloserver/configs/` - 配置文件目录
- `test_config_utils.py` - 功能测试脚本
- `yoloserver/examples/config_usage_example.py` - 使用示例
