# YOLO数据集验证模块使用指南

## 概述

YOLO数据集验证模块提供了完整的数据集质量检查功能，确保在模型训练之前数据集的结构、内容和划分都是正确的。

## 功能特性

### 核心验证功能

1. **数据集配置验证** (`verify_dataset_config`)
   - 验证 `data.yaml` 配置文件格式
   - 检查类别数量与类别名称的一致性
   - 验证图像文件和标签文件的存在性
   - 检查标签文件格式（支持检测和分割任务）
   - 验证坐标值和类别ID的有效性

2. **数据集分割唯一性验证** (`verify_split_uniqueness`)
   - 检查 train、val、test 数据集之间是否存在重复文件
   - 确保数据集分割的独立性

3. **不合法文件删除** (`delete_invalid_files`)
   - 安全删除验证失败的图像和标签文件
   - 提供用户确认机制

## 使用方法

### 命令行使用

#### 基本用法

```bash
# 完整验证检测任务数据集
python scripts/yolo_validate.py --mode FULL --task detection

# 抽样验证分割任务数据集
python scripts/yolo_validate.py --mode SAMPLE --task segmentation

# 验证并启用删除选项
python scripts/yolo_validate.py --mode FULL --task detection --delete-invalid
python scripts/yolo_validate.py --mode FULL --task segmentation --delete-invalid

# 指定自定义配置文件
python scripts/yolo_validate.py --config /path/to/custom/data.yaml --task detection
```

#### 参数说明

- `--mode`: 验证模式
  - `FULL`: 完整验证所有图像（推荐用于最终验证）
  - `SAMPLE`: 抽样验证（快速检查，默认最多100张图像）

- `--task`: 任务类型
  - `detection`: 检测任务（标签格式：class_id x_center y_center width height）
  - `segmentation`: 分割任务（标签格式：class_id x1 y1 x2 y2 ... xn yn）

- `--delete-invalid`: 启用删除选项（可选）
  - 验证失败时提供删除不合法文件的选项
  - 需要用户确认，操作不可逆

- `--config`: 自定义配置文件路径（可选）
  - 默认使用 `configs/data.yaml`

### 编程接口使用

```python
from utils.dataset_validation import (
    verify_dataset_config,
    verify_split_uniqueness,
    delete_invalid_files
)
from utils.logging_utils import setup_logger
from utils.paths import CONFIGS_DIR, LOGS_DIR

# 设置日志
logger = setup_logger(
    base_path=LOGS_DIR,
    log_type="dataset_validation",
    logger_name="My Validation"
)

# 验证数据集配置
yaml_path = CONFIGS_DIR / "data.yaml"
is_valid, invalid_list = verify_dataset_config(
    yaml_path=yaml_path,
    current_logger=logger,
    mode="FULL",
    task_type="segmentation"
)

# 验证分割唯一性
is_unique = verify_split_uniqueness(
    yaml_path=yaml_path,
    current_logger=logger
)

# 删除不合法文件（如果需要）
if not is_valid and invalid_list:
    delete_invalid_files(invalid_list, logger)
```

## 输出示例

### 成功验证

```
🔍 开始验证YOLO数据集...
📁 配置文件: E:\Project\configs\data.yaml
🎯 验证模式: FULL
📋 任务类型: segmentation

📊 步骤 1/2: 验证数据集配置和内容...
✅ 数据集配置验证通过

🔄 步骤 2/2: 验证数据集分割唯一性...
✅ 数据集分割唯一性验证通过

==================================================
📋 验证结果总结:
==================================================
数据集配置验证: ✅ 通过
数据集分割唯一性验证: ✅ 通过
==================================================
🎉 所有验证测试通过！数据集可用于训练
```

### 验证失败

```
🔍 开始验证YOLO数据集...
📁 配置文件: E:\Project\configs\data.yaml
🎯 验证模式: SAMPLE
📋 任务类型: detection

📊 步骤 1/2: 验证数据集配置和内容...
❌ 数据集配置验证失败，发现 251 个问题

🔄 步骤 2/2: 验证数据集分割唯一性...
✅ 数据集分割唯一性验证通过

==================================================
📋 验证结果总结:
==================================================
数据集配置验证: ❌ 失败
数据集分割唯一性验证: ✅ 通过
==================================================
⚠️  部分验证测试失败，请检查数据集
```

## 常见问题

### 1. 标签格式错误

**问题**: "检测任务标签格式错误，第1行应包含5个值，实际23个"

**解决**: 检查任务类型是否正确。如果数据集是分割任务，使用 `--task segmentation`

### 2. 坐标值超出范围

**问题**: "第1行坐标值(1.2)超出范围[0, 1]"

**解决**: YOLO格式要求所有坐标值都在[0, 1]范围内，检查标注工具的输出格式

### 3. 类别ID超出范围

**问题**: "第1行类别ID(5)超出范围[0, 2]"

**解决**: 检查 `data.yaml` 中的类别数量设置，或者检查标签文件中的类别ID

### 4. 文件不存在

**问题**: "标签文件不存在"

**解决**: 确保每张图像都有对应的标签文件，或者删除没有标签的图像

## 日志文件

验证过程会生成详细的日志文件，保存在 `logs/dataset_validation/` 目录下：

- 文件名格式: `temp_YYYYMMDD-HHMMSS.log`
- 包含详细的验证过程和错误信息
- 支持中文编码

## 注意事项

1. **删除操作不可逆**: 使用 `--delete-invalid` 选项时要谨慎，建议先备份数据
2. **抽样验证**: `SAMPLE` 模式只验证部分图像，不能保证100%准确性
3. **性能考虑**: `FULL` 模式在大数据集上可能需要较长时间
4. **路径要求**: 确保 `data.yaml` 中的路径正确且可访问

## 技术要求

- Python 3.8+
- PyYAML
- pathlib (内置)
- logging (内置)
