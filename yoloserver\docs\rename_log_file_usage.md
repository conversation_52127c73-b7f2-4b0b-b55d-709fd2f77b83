# rename_log_file 函数使用指南

## 功能概述

`rename_log_file` 函数用于将临时日志文件重命名为更具描述性和规范性的名称，提高日志的可追溯性和可读性。

## 开发理由

1. **日志文件生命周期管理**：在程序启动初期，我们可能无法立即确定最终的任务名称，因此会先生成带有通用前缀（如 `temp_`）的临时日志文件。
2. **提高可追溯性和可读性**：通过将训练任务的实际名称融入到日志文件名中，使日志文件成为"自描述"的文档。
3. **避免文件冲突和混淆**：确保每个任务都有一个唯一且有意义的日志文件。
4. **灵活性和自动化**：封装重命名逻辑，实现日志文件命名策略的自动化。

## 函数签名

```python
def rename_log_file(logger_obj, save_dir, model_name, encoding="utf-8"):
    """
    重命名日志文件，将临时日志文件重命名为更具描述性的名称
    
    :param logger_obj: 日志记录器对象
    :param save_dir: 保存目录路径，用于提取目录名作为前缀
    :param model_name: 模型名称
    :param encoding: 文件编码，默认 utf-8
    """
```

## 使用示例

### 基本使用

```python
from yoloserver.utils import setup_logger, rename_log_file, LOGS_DIR

# 1. 创建临时日志记录器
logger = setup_logger(
    base_path=LOGS_DIR,
    log_type="training",
    model_name="yolov8n",
    temp_log=True,  # 使用临时文件名
    logger_name="TrainingLogger"
)

# 2. 写入初始日志
logger.info("训练开始...")
logger.info("加载数据集...")

# 3. 在确定最终输出目录后，重命名日志文件
save_dir = "runs/train/train1"  # 训练输出目录
model_name = "yolov8n"

rename_log_file(logger, save_dir, model_name)

# 4. 继续写入日志
logger.info("训练完成")
logger.info("模型已保存")
```

### 推理场景使用

```python
# 推理场景
logger = setup_logger(
    base_path=LOGS_DIR,
    log_type="inference",
    model_name="yolov8n",
    temp_log=True,
    logger_name="InferenceLogger"
)

logger.info("推理开始...")

# 推理完成后重命名
save_dir = "runs/infer/predict1"
rename_log_file(logger, save_dir, "yolov8n")

logger.info("推理完成")
```

## 重命名规则

### 输入文件名格式
- `temp_YYYYMMDD-HHMMSS_modelname.log`
- `log_type_YYYYMMDD-HHMMSS_modelname.log`

### 输出文件名格式
- `{目录前缀}_{时间戳}_{模型名}.log`
- 例如：`train1_20250626-111353_yolov8n.log`

### 冲突处理
如果目标文件名已存在，会自动添加版本号：
- `train1_20250626-111353_yolov8n_v1.log`
- `train1_20250626-111353_yolov8n_v2.log`

## 核心逻辑步骤

1. **遍历日志器处理器列表**：寻找文件处理器
2. **识别文件处理器**：确保是 `FileHandler` 类型
3. **获取旧日志文件路径**：从 `handler.baseFilename` 获取
4. **解析时间戳和构建新文件名**：提取时间戳，组合新文件名
5. **关闭旧的文件处理器**：释放文件句柄
6. **从日志器中移除旧的处理器**：避免继续写入旧文件
7. **执行文件重命名**：使用 `Path.rename()` 重命名文件
8. **添加新的文件处理器**：指向新的日志文件
9. **跳出循环**：完成重命名操作

## 错误处理

### 常见错误情况
1. **无效的日志记录器对象**：函数会记录警告并返回
2. **文件重命名失败**：会创建备用处理器，确保日志连续性
3. **无法解析时间戳**：使用当前时间作为备用时间戳
4. **目标文件已存在**：自动添加版本号避免冲突

### 异常处理机制
```python
try:
    old_log_file.rename(new_log_file)
    logger_obj.info(f"日志文件已成功重命名: {new_log_file.name}")
except OSError as e:
    logger_obj.error(f"重命名日志文件失败: {e}")
    # 创建备用处理器，确保日志连续性
    fallback_handler = logging.FileHandler(old_log_file, encoding=encoding)
    logger_obj.addHandler(fallback_handler)
```

## 最佳实践

1. **在任务开始时使用临时日志**：
   ```python
   logger = setup_logger(..., temp_log=True)
   ```

2. **在确定最终输出目录后立即重命名**：
   ```python
   # 训练开始后，YOLO 确定了输出目录
   rename_log_file(logger, trainer.save_dir, model_name)
   ```

3. **保持模型名称一致性**：
   ```python
   model_name = "yolov8n"  # 在整个流程中保持一致
   ```

4. **处理编码问题**：
   ```python
   rename_log_file(logger, save_dir, model_name, encoding="utf-8-sig")
   ```

## 注意事项

1. **文件句柄管理**：函数会正确关闭旧的文件处理器，避免文件锁定
2. **日志连续性**：即使重命名失败，也会确保日志可以继续写入
3. **跨平台兼容性**：使用 `pathlib.Path` 确保跨平台兼容
4. **线程安全**：函数操作是原子性的，适合多线程环境

## 集成示例

```python
def train_model():
    # 初始化临时日志
    logger = setup_logger(
        base_path=LOGS_DIR,
        log_type="training",
        model_name="yolov8n",
        temp_log=True,
        logger_name="ModelTraining"
    )
    
    logger.info("开始模型训练...")
    
    try:
        # 模拟训练过程
        trainer = YOLO("yolov8n.pt")
        results = trainer.train(data="data.yaml", epochs=100)
        
        # 训练完成后，重命名日志文件
        save_dir = results.save_dir  # 获取实际的保存目录
        rename_log_file(logger, save_dir, "yolov8n")
        
        logger.info("训练完成，日志文件已重命名")
        
    except Exception as e:
        logger.error(f"训练失败: {e}")
        # 即使训练失败，也可以重命名日志文件
        rename_log_file(logger, "runs/train/failed", "yolov8n")
```
