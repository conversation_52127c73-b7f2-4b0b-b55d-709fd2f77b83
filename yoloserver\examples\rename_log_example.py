#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
rename_log_file 函数使用示例

演示如何在实际的训练、验证、推理流程中使用日志重命名功能
"""

import sys
from pathlib import Path
import time

# 添加项目根目录到 Python 路径
project_root = Path(__file__).resolve().parents[2]
sys.path.insert(0, str(project_root))

from yoloserver.utils import setup_logger, rename_log_file, LOGS_DIR


def simulate_training_workflow():
    """模拟训练工作流程"""
    print("=" * 60)
    print("模拟训练工作流程")
    print("=" * 60)
    
    # 1. 初始化临时日志记录器
    logger = setup_logger(
        base_path=LOGS_DIR,
        log_type="training",
        model_name="yolov8n",
        temp_log=True,  # 使用临时文件名
        logger_name="TrainingWorkflow"
    )
    
    # 2. 记录训练开始阶段的日志
    logger.info("=" * 50)
    logger.info("开始模型训练工作流程")
    logger.info("=" * 50)
    logger.info("设备信息: Windows-11, NVIDIA RTX 3070 (8GB)")
    logger.info("模型: YOLOv8n")
    logger.info("数据集: 脑肿瘤检测数据集")
    logger.info("类别: ['glioma_tumor', 'meningioma_tumor', 'pituitary_tumor']")
    
    # 3. 模拟训练配置加载
    logger.info("加载训练配置...")
    logger.info("批大小: 16")
    logger.info("训练轮数: 100")
    logger.info("学习率: 0.001")
    
    # 4. 模拟数据集加载
    logger.info("加载数据集...")
    logger.info("训练集: 2400 张图像")
    logger.info("验证集: 600 张图像")
    logger.info("测试集: 300 张图像")
    
    # 5. 模拟训练开始（此时确定了输出目录）
    save_dir = "runs/train/train1"  # 模拟 YOLO 确定的输出目录
    logger.info(f"训练输出目录: {save_dir}")
    
    # 6. 重命名日志文件
    print(f"\n🔄 重命名日志文件...")
    print(f"   输出目录: {save_dir}")
    print(f"   模型名称: yolov8n")
    
    rename_log_file(logger, save_dir, "yolov8n")
    
    # 7. 继续记录训练过程
    logger.info("开始训练循环...")
    for epoch in range(1, 4):  # 模拟前3个epoch
        logger.info(f"Epoch {epoch}/100:")
        logger.info(f"  训练损失: {0.5 - epoch * 0.1:.3f}")
        logger.info(f"  验证损失: {0.4 - epoch * 0.08:.3f}")
        logger.info(f"  mAP@0.5: {0.6 + epoch * 0.1:.3f}")
        time.sleep(0.1)  # 模拟训练时间
    
    logger.info("训练完成!")
    logger.info("最佳模型已保存")
    logger.info("训练日志已重命名为规范格式")
    
    print("✅ 训练工作流程完成")


def simulate_inference_workflow():
    """模拟推理工作流程"""
    print("\n" + "=" * 60)
    print("模拟推理工作流程")
    print("=" * 60)
    
    # 1. 初始化临时日志记录器
    logger = setup_logger(
        base_path=LOGS_DIR,
        log_type="inference",
        model_name="yolov8n",
        temp_log=True,
        logger_name="InferenceWorkflow"
    )
    
    # 2. 记录推理开始阶段的日志
    logger.info("=" * 50)
    logger.info("开始模型推理工作流程")
    logger.info("=" * 50)
    logger.info("设备信息: Windows-11, NVIDIA RTX 3070 (8GB)")
    logger.info("模型权重: runs/train/train1/weights/best.pt")
    logger.info("推理源: data/test/images")
    logger.info("置信度阈值: 0.25")
    logger.info("IoU阈值: 0.7")
    
    # 3. 模拟推理过程
    logger.info("加载模型权重...")
    logger.info("预处理输入图像...")
    logger.info("开始批量推理...")
    
    # 4. 模拟推理结果（此时确定了输出目录）
    save_dir = "runs/infer/predict1"  # 模拟推理输出目录
    logger.info(f"推理输出目录: {save_dir}")
    
    # 5. 重命名日志文件
    print(f"\n🔄 重命名日志文件...")
    print(f"   输出目录: {save_dir}")
    print(f"   模型名称: yolov8n")
    
    rename_log_file(logger, save_dir, "yolov8n")
    
    # 6. 继续记录推理结果
    logger.info("推理统计结果:")
    logger.info("  处理图像数: 300")
    logger.info("  检测到的肿瘤数: 245")
    logger.info("  - 胶质瘤: 89")
    logger.info("  - 脑膜瘤: 76")
    logger.info("  - 垂体瘤: 80")
    logger.info("  平均置信度: 0.87")
    logger.info("  推理速度: 15.2ms/图像")
    
    logger.info("推理完成!")
    logger.info("结果已保存到输出目录")
    logger.info("推理日志已重命名为规范格式")
    
    print("✅ 推理工作流程完成")


def simulate_validation_workflow():
    """模拟验证工作流程"""
    print("\n" + "=" * 60)
    print("模拟验证工作流程")
    print("=" * 60)
    
    # 1. 初始化临时日志记录器
    logger = setup_logger(
        base_path=LOGS_DIR,
        log_type="validation",
        model_name="yolov8n",
        temp_log=True,
        logger_name="ValidationWorkflow"
    )
    
    # 2. 记录验证开始阶段的日志
    logger.info("=" * 50)
    logger.info("开始模型验证工作流程")
    logger.info("=" * 50)
    logger.info("模型权重: runs/train/train1/weights/best.pt")
    logger.info("验证数据集: data/val")
    logger.info("验证图像数: 600")
    
    # 3. 模拟验证过程
    logger.info("加载验证数据集...")
    logger.info("开始模型验证...")
    
    # 4. 模拟验证结果（此时确定了输出目录）
    save_dir = "runs/val/val1"  # 模拟验证输出目录
    logger.info(f"验证输出目录: {save_dir}")
    
    # 5. 重命名日志文件
    print(f"\n🔄 重命名日志文件...")
    print(f"   输出目录: {save_dir}")
    print(f"   模型名称: yolov8n")
    
    rename_log_file(logger, save_dir, "yolov8n")
    
    # 6. 继续记录验证结果
    logger.info("验证指标:")
    logger.info("  mAP@0.5: 0.892")
    logger.info("  mAP@0.5:0.95: 0.654")
    logger.info("  精确率: 0.876")
    logger.info("  召回率: 0.834")
    logger.info("  F1分数: 0.855")
    
    logger.info("各类别性能:")
    logger.info("  胶质瘤 - mAP@0.5: 0.901")
    logger.info("  脑膜瘤 - mAP@0.5: 0.888")
    logger.info("  垂体瘤 - mAP@0.5: 0.887")
    
    logger.info("验证完成!")
    logger.info("验证报告已生成")
    logger.info("验证日志已重命名为规范格式")
    
    print("✅ 验证工作流程完成")


def main():
    """主函数"""
    print("🚀 rename_log_file 函数使用示例")
    print("演示在实际工作流程中的日志重命名功能")
    
    try:
        # 运行各种工作流程示例
        simulate_training_workflow()
        simulate_inference_workflow()
        simulate_validation_workflow()
        
        print("\n" + "=" * 60)
        print("🎉 所有工作流程示例完成!")
        print("=" * 60)
        print("请检查以下目录中的重命名日志文件:")
        print(f"  📁 {LOGS_DIR}/training/")
        print(f"  📁 {LOGS_DIR}/inference/")
        print(f"  📁 {LOGS_DIR}/validation/")
        print("\n日志文件命名格式: {目录前缀}_{时间戳}_{模型名}.log")
        print("例如: train1_20250626-111353_yolov8n.log")
        
    except Exception as e:
        print(f"❌ 示例运行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
