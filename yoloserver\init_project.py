#!/usr/bin/env python3
"""
医学图像检测与分割系统 - 项目初始化脚本

该脚本用于自动创建项目所需的完整目录结构，确保项目在任何环境下都能正常运行。
包含日志记录、性能监控、用户指引等功能。

功能特性：
1. 自动创建所有必须的目录结构
2. 为空目录添加.keep文件以支持Git版本控制
3. 详细的日志记录和用户反馈
4. 性能监控和执行时间统计
5. 用户操作指引和提示

"""

import os
import sys
import time
from pathlib import Path
from typing import List, Dict
from utils import setup_logger
from utils.performance_utils import time_it, format_time_auto_unit
from utils import (
        # 主要架构目录
        YOLOSERVER_ROOT,
        # 配置目录
        CONFIGS_DIR,
        # 模型目录
        MODELS_DIR,
        CHECKPOINTS_DIR,
        PRETRAINED_DIR,
        ONNX_MODELS_DIR,
        TENSORRT_MODELS_DIR,
        # 数据目录
        DATA_DIR,
        RAW_DATA_DIR,
        TEMP_DATA_DIR,
        # 标注目录
        ORIGINAL_ANNOTATIONS_DIR,
        RAW_IMAGES_DIR,
        YOLO_ANNOTATIONS_DIR,
        COCO_ANNOTATIONS_DIR,
        # 训练数据集目录
        TRAIN_DIR,
        VAL_DIR,
        TEST_DIR,
        # 运行结果目录
        RUNS_DIR,
        # 日志目录
        LOGS_DIR,
        # 脚本目录
        SCRIPTS_DIR,
        # 文档目录
        DOCS_DIR,
        # 测试目录
        TESTS_DIR,
)
# 添加utils目录到Python路径
sys.path.append(str(Path(__file__).parent / "utils"))

try:
    from logging_utils import setup_logger
    from performance_utils import time_it, format_time_auto_unit
except ImportError as e:
    print(f"错误：无法导入工具模块: {e}")
    print("请确保utils目录下存在logging_utils.py和performance_utils.py文件")
    sys.exit(1)


class ProjectInitializer:
    """项目初始化器类"""
    
    def __init__(self):
        """初始化项目初始化器"""
        self.yoloserver_root = YOLOSERVER_ROOT
        self.start_time = time.perf_counter()

        # 初始化日志系统
        self.logs_dir = LOGS_DIR
        self.logs_dir.mkdir(parents=True, exist_ok=True)

        self.logger = setup_logger(
            base_path=self.logs_dir,
            log_type="init_log",
            model_name="project_init",
            logger_name="ProjectInitializer",
            temp_log=True
        )
        
        # 统计信息
        self.stats = {
            "created_dirs": [],
            "existing_dirs": [],
            "failed_dirs": [],
            "created_keep_files": [],
            "existing_keep_files": [],
            "failed_keep_files": []
        }
        
        self.logger.info("项目初始化器启动")
        self.logger.info(f"项目根目录: {YOLOSERVER_ROOT}")
        
    def get_directory_structure(self) -> Dict[str, List[Path]]:
        """
        定义完整的目录结构
        返回按类别组织的目录路径字典
        """
        structure = {
            "配置文件目录": [
                CONFIGS_DIR
            ],

            "模型相关目录": [
                MODELS_DIR,
                CHECKPOINTS_DIR,
                PRETRAINED_DIR,
                ONNX_MODELS_DIR,
                TENSORRT_MODELS_DIR
            ],

            "数据管理目录": [
                DATA_DIR,
                RAW_DATA_DIR,
                TEMP_DATA_DIR
            ],

            "原始医学影像数据目录": [
                RAW_IMAGES_DIR,
                ORIGINAL_ANNOTATIONS_DIR
            ],

            "标注数据目录": [
                YOLO_ANNOTATIONS_DIR,
                COCO_ANNOTATIONS_DIR
            ],
            
            "训练数据集目录": [
                TRAIN_DIR,
                VAL_DIR,
                TEST_DIR,
                TRAIN_DIR / "images",
                TRAIN_DIR / "labels",
                VAL_DIR / "images",
                VAL_DIR / "labels",
                TEST_DIR / "images",
                TEST_DIR / "labels"
            ],

            "运行结果目录": [
                RUNS_DIR,
                RUNS_DIR / "train",
                RUNS_DIR / "detect",
                RUNS_DIR / "segment",
                RUNS_DIR / "export"
            ],

            "日志和监控目录": [
                LOGS_DIR,
                LOGS_DIR / "app_log",
                LOGS_DIR / "models_log",
                LOGS_DIR / "api_log",
                LOGS_DIR / "errors_log",
                LOGS_DIR / "inference_log",
                LOGS_DIR / "training_log",
                LOGS_DIR / "init_log",
                LOGS_DIR / "test_log"
            ],

            "脚本和工具目录": [
                SCRIPTS_DIR,
                SCRIPTS_DIR / "training",
                SCRIPTS_DIR / "inference",
                SCRIPTS_DIR / "data_processing",
                SCRIPTS_DIR / "deployment"
            ],

            "文档目录": [
                DOCS_DIR
            ],

            "测试目录": [
                TESTS_DIR
            ]
        }
        
        return structure

    @time_it(iterations=1, name="目录创建")
    def create_directories(self) -> None:
        """创建所有必要的目录"""
        self.logger.info("开始创建项目目录结构...")

        structure = self.get_directory_structure()

        for category, directories in structure.items():
            self.logger.info(f"正在处理 {category}...")

            for directory in directories:
                try:
                    if directory.exists():
                        self.logger.info(f"     目录已存在: {directory.relative_to(YOLOSERVER_ROOT)}")
                        self.stats["existing_dirs"].append(str(directory.relative_to(YOLOSERVER_ROOT)))
                    else:
                        directory.mkdir(parents=True, exist_ok=True)
                        self.logger.info(f"  目录已创建: {directory.relative_to(YOLOSERVER_ROOT)}")
                        self.stats["created_dirs"].append(str(directory.relative_to(YOLOSERVER_ROOT)))

                except Exception as e:
                    self.logger.error(f"  创建目录失败: {directory.relative_to(YOLOSERVER_ROOT)} - {e}")
                    self.stats["failed_dirs"].append(str(directory.relative_to(YOLOSERVER_ROOT)))

    # 检查原始数据集目录是否存在，并检查是否为空
    @time_it(iterations=1, name="原始数据目录检查")
    def check_raw_data_directory(self) -> None:
        """
        检查原始数据集目录是否存在，并检查是否为空

        检查内容：
        1. 原始数据根目录是否存在
        2. 原始图像目录是否存在且不为空
        3. 原始标注目录是否存在且不为空
        4. 统计文件数量并记录详细信息
        """
        raw_data_dir = RAW_DATA_DIR
        raw_images_dir = RAW_IMAGES_DIR
        raw_annotations_dir = ORIGINAL_ANNOTATIONS_DIR

        self.logger.info("=" * 80)
        self.logger.info("开始检查原始数据集目录...")
        self.logger.info("=" * 80)

        # 检查原始数据根目录
        if not raw_data_dir.exists():
            msg = f"原始数据集根目录不存在: {raw_data_dir.relative_to(YOLOSERVER_ROOT)}"
            self.logger.error(msg)
            self.logger.error("请确保项目结构正确，以便数据集转换正常执行")
            raise Exception(msg)
        else:
            self.logger.info(f"原始数据集根目录存在: {raw_data_dir.relative_to(YOLOSERVER_ROOT)}")

        # 检查原始图像目录
        if not raw_images_dir.exists():
            msg = f"原始图像目录不存在: {raw_images_dir.relative_to(YOLOSERVER_ROOT)}"
            self.logger.error(msg)
            raise Exception(msg)
        else:
            self.logger.info(f"原始图像目录存在: {raw_images_dir.relative_to(YOLOSERVER_ROOT)}")

            # 统计图像文件
            try:
                image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif']
                image_files = []
                for ext in image_extensions:
                    # 避免重复统计，使用集合去重
                    files_lower = set(raw_images_dir.glob(f'*{ext}'))
                    files_upper = set(raw_images_dir.glob(f'*{ext.upper()}'))
                    image_files.extend(list(files_lower | files_upper))

                if image_files:
                    self.logger.info(f"发现 {len(image_files)} 个图像文件")
                    # 按扩展名分类统计
                    ext_count = {}
                    for img_file in image_files:
                        ext = img_file.suffix.lower()
                        ext_count[ext] = ext_count.get(ext, 0) + 1

                    for ext, count in ext_count.items():
                        self.logger.info(f"  - {ext} 格式: {count} 个文件")
                else:
                    self.logger.warning(f"原始图像目录为空: {raw_images_dir.relative_to(YOLOSERVER_ROOT)}")
                    self.logger.warning("请将医学影像文件放置到此目录中")

            except Exception as e:
                self.logger.error(f"统计图像文件时发生错误: {e}")

        # 检查原始标注目录
        if not raw_annotations_dir.exists():
            msg = f"原始标注目录不存在: {raw_annotations_dir.relative_to(YOLOSERVER_ROOT)}"
            self.logger.error(msg)
            raise Exception(msg)
        else:
            self.logger.info(f"原始标注目录存在: {raw_annotations_dir.relative_to(YOLOSERVER_ROOT)}")

            # 统计标注文件
            try:
                annotation_extensions = ['.json', '.xml', '.txt', '.csv', '.yaml', '.yml']
                annotation_files = []
                for ext in annotation_extensions:
                    annotation_files.extend(list(raw_annotations_dir.glob(f'*{ext}')))
                    annotation_files.extend(list(raw_annotations_dir.glob(f'*{ext.upper()}')))

                if annotation_files:
                    self.logger.info(f"发现 {len(annotation_files)} 个标注文件")
                    # 按扩展名分类统计
                    ext_count = {}
                    for ann_file in annotation_files:
                        ext = ann_file.suffix.lower()
                        ext_count[ext] = ext_count.get(ext, 0) + 1

                    for ext, count in ext_count.items():
                        self.logger.info(f"  - {ext} 格式: {count} 个文件")
                else:
                    self.logger.warning(f"原始标注目录为空: {raw_annotations_dir.relative_to(YOLOSERVER_ROOT)}")
                    self.logger.warning("请将标注文件放置到此目录中")

            except Exception as e:
                self.logger.error(f"统计标注文件时发生错误: {e}")

        # 检查数据完整性
        try:
            image_files = []
            annotation_files = []

            # 重新统计文件（用于完整性检查）
            image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif']
            for ext in image_extensions:
                image_files.extend(list(raw_images_dir.glob(f'*{ext}')))
                image_files.extend(list(raw_images_dir.glob(f'*{ext.upper()}')))

            annotation_extensions = ['.json', '.xml', '.txt', '.csv', '.yaml', '.yml']
            for ext in annotation_extensions:
                annotation_files.extend(list(raw_annotations_dir.glob(f'*{ext}')))
                annotation_files.extend(list(raw_annotations_dir.glob(f'*{ext.upper()}')))

            if not image_files and not annotation_files:
                msg = "原始数据集目录存在但完全为空，无法进行数据处理"
                self.logger.error(msg)
                self.logger.error("请添加医学影像文件和对应的标注文件")
                raise Exception(msg)
            elif not image_files:
                msg = "缺少图像文件，无法进行训练"
                self.logger.error(msg)
                self.logger.error("请将医学影像文件添加到 data/raw/images/ 目录")
                raise Exception(msg)
            elif not annotation_files:
                self.logger.warning("缺少标注文件，仅可进行推理，无法训练模型")
                self.logger.warning("如需训练模型，请将标注文件添加到 data/raw/original_annotations/ 目录")
            else:
                self.logger.info("数据集检查完成，发现图像和标注文件")

        except Exception as e:
            self.logger.error(f"数据完整性检查时发生错误: {e}")

        self.logger.info("=" * 80)


    @time_it(iterations=1, name=".keep文件创建")
    def create_keep_files(self) -> None:
        """为空目录创建.keep文件以支持Git版本控制"""
        self.logger.info("开始为空目录创建.keep文件...")

        structure = self.get_directory_structure()
        all_directories = []

        # 收集所有目录
        for directories in structure.values():
            all_directories.extend(directories)

        for directory in all_directories:
            if not directory.exists():
                continue

            keep_file = directory / ".keep"

            try:
                # 检查目录是否为空（除了可能存在的.keep文件）
                dir_contents = [f for f in directory.iterdir() if f.name != ".keep"]

                if not dir_contents:  # 目录为空
                    if keep_file.exists():
                        self.logger.info(f"  .keep文件已存在: {directory.relative_to(YOLOSERVER_ROOT)}/.keep")
                        self.stats["existing_keep_files"].append(str(directory.relative_to(YOLOSERVER_ROOT)))
                    else:
                        keep_file.touch()
                        self.logger.info(f"  .keep文件已创建: {directory.relative_to(YOLOSERVER_ROOT)}/.keep")
                        self.stats["created_keep_files"].append(str(directory.relative_to(YOLOSERVER_ROOT)))

            except Exception as e:
                self.logger.error(f"  创建.keep文件失败: {directory.relative_to(YOLOSERVER_ROOT)}/.keep - {e}")
                self.stats["failed_keep_files"].append(str(directory.relative_to(YOLOSERVER_ROOT)))

    def print_user_guidance(self) -> None:
        """打印用户操作指引"""
        guidance_text = """
╔══════════════════════════════════════════════════════════════════════════════
║                            用户操作指引
╠══════════════════════════════════════════════════════════════════════════════
║
║ 项目初始化已完成！以下是您需要了解的重要信息：
║
║ 需要您手动添加内容的目录：
║    • data/raw/images/           - 请将原始医学影像文件放置在此目录
║    • data/raw/original_annotations/ - 请将原始标注文件放置在此目录
║    • models/pretrained/         - 请下载并放置预训练模型文件
║    • configs/                   - 请根据需要添加配置文件
║
║ 自动管理的目录（无需手动操作）：
║    • runs/                      - 训练和推理结果将自动保存在此
║    • logs/                      - 所有日志文件将自动生成
║    • data/processed/            - 处理后的数据将自动保存
║    • data/temp/                 - 临时文件将自动管理
║
║ 下一步建议操作：
║    1. 将医学影像数据复制到 data/raw/images/ 目录
║    2. 将标注文件复制到 data/raw/original_annotations/ 目录
║    3. 下载所需的预训练模型到 models/pretrained/ 目录
║    4. 根据项目需求配置 configs/ 目录下的配置文件
║    5. 运行数据预处理脚本准备训练数据
║
║ 重要提醒：
║    • 请勿删除 .keep 文件，它们确保空目录能被Git正确跟踪
║    • logs/ 目录下的文件会自动生成，无需手动创建
║    • 如需重新初始化，可以重新运行此脚本
╚══════════════════════════════════════════════════════════════════════════════
        """

        print(guidance_text)
        self.logger.info("用户操作指引已显示")

    def print_statistics(self) -> None:
        """打印详细的统计信息"""
        total_time = time.perf_counter() - self.start_time
        formatted_time = format_time_auto_unit(total_time)

        stats_text = f"""
╔══════════════════════════════════════════════════════════════════════════════
║                            初始化统计报告
╠══════════════════════════════════════════════════════════════════════════════
║
║ 总执行时间: {formatted_time:<20}
║
║ 目录创建统计:
║    • 新创建目录: {len(self.stats['created_dirs']):<3} 个
║    • 已存在目录: {len(self.stats['existing_dirs']):<3} 个
║    • 创建失败目录: {len(self.stats['failed_dirs']):<3} 个
║
║ .keep文件统计:
║    • 新创建.keep文件: {len(self.stats['created_keep_files']):<3} 个
║    • 已存在.keep文件: {len(self.stats['existing_keep_files']):<3} 个
║    • 创建失败.keep文件: {len(self.stats['failed_keep_files']):<3} 个
║
╚══════════════════════════════════════════════════════════════════════════════
        """

        print(stats_text)

        # 详细列出新创建的目录
        if self.stats['created_dirs']:
            self.logger.info("新创建的目录:")
            for dir_path in self.stats['created_dirs']:
                self.logger.info(f"  • {dir_path}")

        # 详细列出新创建的.keep文件
        if self.stats['created_keep_files']:
            self.logger.info("新创建的.keep文件:")
            for dir_path in self.stats['created_keep_files']:
                self.logger.info(f"  • {dir_path}/.keep")

        # 报告任何失败的操作
        if self.stats['failed_dirs']:
            self.logger.warning("创建失败的目录:")
            for dir_path in self.stats['failed_dirs']:
                self.logger.warning(f"  • {dir_path}")

        if self.stats['failed_keep_files']:
            self.logger.warning("创建失败的.keep文件:")
            for dir_path in self.stats['failed_keep_files']:
                self.logger.warning(f"  • {dir_path}/.keep")

    @time_it(iterations=1, name="项目初始化")
    def run(self) -> bool:
        """运行完整的项目初始化流程"""
        try:
            self.logger.info("=" * 80)
            self.logger.info("开始项目初始化流程".center(80))
            self.logger.info("=" * 80)

            # 1. 创建目录结构
            self.create_directories()

            # 2. 检查原始数据集目录
            self.check_raw_data_directory()

            # 3. 创建.keep文件
            self.create_keep_files()

            # 4. 显示用户指引
            self.print_user_guidance()

            # 5. 显示统计信息
            self.print_statistics()

            self.logger.info("=" * 80)
            self.logger.info("项目初始化完成".center(80))
            self.logger.info("=" * 80)

            return True

        except Exception as e:
            self.logger.error(f"项目初始化过程中发生错误: {e}")
            return False


def main():
    """主函数"""
    print("医学图像检测与分割系统 - 项目初始化脚本")
    print("=" * 80)

    try:
        initializer = ProjectInitializer()
        success = initializer.run()

        if success:
            print("\n项目初始化成功完成！")
            print("请查看上方的用户操作指引了解下一步操作")
            return 0
        else:
            print("\n项目初始化过程中遇到问题，请查看日志了解详情")
            return 1

    except KeyboardInterrupt:
        print("\n\n用户中断了初始化过程")
        return 1
    except Exception as e:
        print(f"\n初始化过程中发生未预期的错误: {e}")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
