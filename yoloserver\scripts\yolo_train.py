from ultralytics import YOLO
import argparse
from pathlib import Path
import sys
import logging

current_path = Path(__file__).parent.parent.resolve()
utils_path = current_path / 'utils'
if str(current_path) not in sys.path:
    sys.path.insert(0, str(current_path))
if str(utils_path) not in sys.path:
    sys.path.insert(1, str(utils_path))


from utils import setup_logger, rename_log_file, time_it
from paths import LOGS_DIR, PRETRAINED_DIR, CHECKPOINTS_DIR
from config_utils import load_config, merge_config
from model_utils import copy_checkpoint_models
from utils.system_utils import log_device_info
from utils.datainfo_utils import log_dataset_info
from utils.logging_utils import log_parameters
from utils.result_utils import log_results

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="YOLO模型训练工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python yolo_train.py --model yolov8n.pt --epochs 100
        """
    )

    parser.add_argument(
        '--data',
        type=str,
        default='data.yaml',
        help='数据集配置文件路径 (默认: data.yaml)'
    )

    parser.add_argument(
        '--batch',
        type=int,
        default=16,
        help='批次大小 (默认: 16)'
    )

    parser.add_argument(
        '--epochs',
        type=int,
        default=100,
        help='训练轮数 (默认: 100)'
    )

    parser.add_argument(
        '--imgsz',
        type=int,
        default=640,
        help='输入图像尺寸 (默认: 640)'
    )

    parser.add_argument(
        '--device',
        type=str,
        default='0',
        help='训练设备 (默认: 0)'
    )

    parser.add_argument(
        '--weights',
        type=str,
        default="yolo11n-seg",
        help='预训练权重文件路径 (默认: yolo11n-seg)'
    )

    parser.add_argument(
        '--workers',
        type=int,
        default=8,
        help='数据加载线程数 (默认: 8)'
    )

    # 自定义参数
    parser.add_argument(
        '--use_yaml',
        type=bool,
        default=True,
        help='是否使用 YAML 配置文件覆盖命令行参数 (默认: True)'
    )

    return parser.parse_args()

def run_training(model, yolo_args):
    """执行训练"""
    # 将Namespace转换为字典
    if hasattr(yolo_args, '__dict__'):
        args_dict = vars(yolo_args)
    else:
        args_dict = yolo_args
    results = model.train(**args_dict)
    return results

def main(args):
    logger = logging.getLogger("YOLO Train")
    logging.info("YOLO 肿瘤检测模型训练脚本启动".center(80, "="))

    try:
        if args.use_yaml:
            yaml_config = load_config(config_type='train')

        # 获取设备信息
        log_device_info(logger)

        # 获取数据集信息
        log_dataset_info(data_config_name="data.yaml", mode="train", logger=logger)

        # 合并参数
        yolo_args, project_args = merge_config(args, yaml_config, mode='train')

        # 记录命令行参数和YAML参数信息
        exclude_params = {'use_yaml'}  # 排除不需要记录的参数
        log_parameters(project_args, exclude_params, logger)

        # 初始化模型，开始执行训练
        logger.info(f"初始化模型，加载模型{project_args.weights}")

        # 处理权重文件路径，如果没有扩展名则添加.pt
        weights_name = project_args.weights
        if not weights_name.endswith('.pt'):
            weights_name += '.pt'

        model_path = PRETRAINED_DIR / weights_name
        if not model_path.exists():
            logger.warning(f"预训练模型 {model_path} 不存在，请将{weights_name}下载到{PRETRAINED_DIR}目录")
            # 尝试直接使用权重名称（让YOLO自动下载）
            logger.info(f"尝试使用YOLO自动下载功能加载模型: {project_args.weights}")
            model_path = project_args.weights

        model = YOLO(model_path)

        # 动态引用time_it装饰器
        decorate_run_training = time_it(iterations=1, name="模型训练", logger_instance=logger)
        decorated_run_training = decorate_run_training(run_training)

        results = decorated_run_training(model, yolo_args)

        # 记录结果信息
        log_results(results, logger=logger, model_trainer=model.trainer)

        # 复制模型
        # 从训练器获取保存目录
        save_dir = getattr(model.trainer, 'save_dir', None) if hasattr(model, 'trainer') else None
        if save_dir:
            copy_checkpoint_models(save_dir, project_args.weights, CHECKPOINTS_DIR, logger)
        else:
            logger.warning("无法获取训练保存目录，跳过模型复制")

        # 重命名日志文件
        if save_dir:
            rename_log_file(logger, save_dir, project_args.weights)
        else:
            logger.warning("无法获取训练保存目录，跳过日志文件重命名")

        logger.info(f"YOLO 肿瘤检测脚本训练完成")

    except Exception as e:
        logger.error(f"训练失败: {e}")
        raise e

if __name__ == "__main__":
    args_ = parse_args()
    logger = setup_logger(
        base_path=LOGS_DIR,
        model_name=args_.weights.replace(".pt", ""),
        log_type="train",
        log_level=logging.INFO,
        temp_log=False,
        logger_name="YOLO Train"
    )
    main(args_)
