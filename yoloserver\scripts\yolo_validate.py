#!/usr/bin/env python3
"""
医学图像检测与分割系统 - YOLO数据集验证脚本

该脚本提供命令行接口，用于验证YOLO数据集的结构、内容和划分正确性。
在模型训练之前确保数据集的高质量，避免训练过程中的错误。

使用方法:
    python yolo_validate.py --mode FULL --task detection
    python yolo_validate.py --mode SAMPLE --task segmentation --delete-invalid

参数说明:
    --mode: 验证模式，FULL (完整验证) 或 SAMPLE (抽样验证)
    --task: 任务类型，detection 或 segmentation  
    --delete-invalid: 是否在验证失败后启用删除选项
"""

import sys
import argparse
import logging
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = Path(__file__).resolve().parent
project_root = current_dir.parent
sys.path.insert(0, str(project_root))

# 导入项目模块
from utils.paths import CONFIGS_DIR, LOGS_DIR
from utils.logging_utils import setup_logger
from utils.dataset_validation import (
    verify_dataset_config,
    verify_split_uniqueness, 
    delete_invalid_files
)


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="YOLO数据集验证工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python yolo_validate.py --mode FULL --task detection
  python yolo_validate.py --mode SAMPLE --task segmentation --delete-invalid
        """
    )
    
    parser.add_argument(
        '--mode',
        type=str,
        choices=['FULL', 'SAMPLE'],
        default='FULL',
        help='验证模式: FULL (完整验证) 或 SAMPLE (抽样验证，默认: FULL)'
    )
    
    parser.add_argument(
        '--task',
        type=str,
        choices=['detection', 'segmentation'],
        default='detection',
        help='任务类型: detection 或 segmentation (默认: detection)'
    )
    
    parser.add_argument(
        '--delete-invalid',
        action='store_true',
        help='是否在验证失败后启用删除选项'
    )
    
    parser.add_argument(
        '--config',
        type=str,
        default=None,
        help='data.yaml配置文件路径 (默认: configs/data.yaml)'
    )
    
    return parser.parse_args()


def setup_validation_logger():
    """设置验证专用的日志记录器"""
    logger = setup_logger(
        base_path=LOGS_DIR,
        log_type="dataset_validation",
        model_name=None,
        log_level=logging.INFO,
        temp_log=True,
        logger_name="Dataset Validation"
    )
    return logger


def confirm_deletion(invalid_count: int) -> bool:
    """
    询问用户是否确认删除不合法文件
    
    Args:
        invalid_count (int): 不合法文件数量
        
    Returns:
        bool: 用户确认结果
    """
    print(f"\n⚠️  警告: 发现 {invalid_count} 个不合法样本")
    print("删除操作不可逆，将同时删除图像文件和对应的标签文件")
    
    while True:
        response = input("是否确认删除这些不合法文件? (y/N): ").strip().lower()
        if response in ['y', 'yes']:
            return True
        elif response in ['n', 'no', '']:
            return False
        else:
            print("请输入 'y' 或 'n'")


def main():
    """主函数"""
    # 解析命令行参数
    args = parse_arguments()
    
    # 设置日志记录器
    logger = setup_validation_logger()
    
    # 确定配置文件路径
    if args.config:
        yaml_path = Path(args.config)
    else:
        yaml_path = CONFIGS_DIR / "data.yaml"
    
    if not yaml_path.exists():
        logger.error(f"配置文件不存在: {yaml_path}")
        print(f"❌ 错误: 配置文件不存在: {yaml_path}")
        sys.exit(1)
    
    # 输出验证信息
    logger.info("=" * 60)
    logger.info("开始YOLO数据集验证")
    logger.info(f"验证模式: {args.mode}")
    logger.info(f"任务类型: {args.task}")
    logger.info(f"配置文件: {yaml_path}")
    logger.info(f"删除选项: {'启用' if args.delete_invalid else '禁用'}")
    logger.info("=" * 60)
    
    print(f"🔍 开始验证YOLO数据集...")
    print(f"📁 配置文件: {yaml_path}")
    print(f"🎯 验证模式: {args.mode}")
    print(f"📋 任务类型: {args.task}")
    
    # 执行验证
    validation_results = []
    
    # 1. 验证数据集配置和内容
    print("\n📊 步骤 1/2: 验证数据集配置和内容...")
    config_valid, invalid_data_list = verify_dataset_config(
        yaml_path=yaml_path,
        current_logger=logger,
        mode=args.mode,
        task_type=args.task
    )
    
    validation_results.append(("数据集配置验证", config_valid))
    
    if config_valid:
        print("✅ 数据集配置验证通过")
    else:
        print(f"❌ 数据集配置验证失败，发现 {len(invalid_data_list)} 个问题")
    
    # 处理不合法文件删除
    if not config_valid and invalid_data_list and args.delete_invalid:
        if confirm_deletion(len(invalid_data_list)):
            print("\n🗑️  正在删除不合法文件...")
            delete_invalid_files(invalid_data_list, logger)
            print("✅ 不合法文件删除完成")
        else:
            print("❌ 用户取消删除操作")
    
    # 2. 验证数据集分割唯一性
    print("\n🔄 步骤 2/2: 验证数据集分割唯一性...")
    split_unique = verify_split_uniqueness(
        yaml_path=yaml_path,
        current_logger=logger
    )
    
    validation_results.append(("数据集分割唯一性验证", split_unique))
    
    if split_unique:
        print("✅ 数据集分割唯一性验证通过")
    else:
        print("❌ 数据集分割唯一性验证失败，存在重复文件")
    
    # 输出最终结果
    print("\n" + "=" * 50)
    print("📋 验证结果总结:")
    print("=" * 50)
    
    all_passed = True
    for test_name, result in validation_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    print("=" * 50)
    
    if all_passed:
        print("所有验证测试通过！数据集可用于训练")
        logger.info("所有验证测试通过，数据集验证成功")
        sys.exit(0)
    else:
        print("部分验证测试失败，请检查数据集")
        logger.warning("部分验证测试失败，数据集验证未通过")
        sys.exit(1)


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n❌ 用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 发生未预期的错误: {str(e)}")
        sys.exit(1)
