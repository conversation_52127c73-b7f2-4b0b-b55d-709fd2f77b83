#!/usr/bin/env python3
"""
项目初始化脚本测试

该脚本用于测试init_project.py的功能，验证目录创建和.keep文件生成是否正常工作。

"""

import os
import sys
import shutil
from pathlib import Path

def test_init_script():
    """测试初始化脚本功能"""
    print("开始测试项目初始化脚本...")
    
    # 测试目录
    test_dir = Path("test_temp_dir")
    
    try:
        # 1. 创建测试目录
        if test_dir.exists():
            shutil.rmtree(test_dir)
        test_dir.mkdir()
        
        print(f"✓ 创建测试目录: {test_dir}")
        
        # 2. 删除测试目录
        shutil.rmtree(test_dir)
        print(f"✓ 删除测试目录: {test_dir}")
        
        # 3. 运行初始化脚本
        print("✓ 运行初始化脚本...")
        import init_project
        initializer = init_project.ProjectInitializer()
        success = initializer.run()
        
        if success:
            print("初始化脚本测试通过！")
            
            # 4. 验证关键目录是否存在
            key_dirs = [
                "configs/models",
                "configs/api", 
                "configs/deployment",
                "models/checkpoints",
                "models/pretrained",
                "data/raw/images",
                "data/raw/original_annotations",
                "data/train/images",
                "data/train/labels",
                "logs/init_log",
                "scripts/training"
            ]
            
            print("\n验证关键目录:")
            all_exist = True
            for dir_path in key_dirs:
                full_path = Path(dir_path)
                if full_path.exists():
                    print(f"  ✓ {dir_path}")
                else:
                    print(f"  ✗ {dir_path} - 不存在")
                    all_exist = False
            
            # 5. 验证.keep文件
            print("\n验证.keep文件:")
            keep_files_checked = 0
            keep_files_found = 0
            
            for dir_path in key_dirs:
                full_path = Path(dir_path)
                if full_path.exists():
                    keep_file = full_path / ".keep"
                    keep_files_checked += 1
                    if keep_file.exists():
                        keep_files_found += 1
                        print(f"  ✓ {dir_path}/.keep")
                    else:
                        # 检查目录是否为空（除了可能的其他文件）
                        dir_contents = [f for f in full_path.iterdir() if f.name != ".keep"]
                        if not dir_contents:  # 目录为空，应该有.keep文件
                            print(f"  ✗ {dir_path}/.keep - 空目录缺少.keep文件")
                        else:
                            print(f"  - {dir_path}/.keep - 目录非空，无需.keep文件")
            
            print(f"\n测试结果:")
            print(f"  • 检查的目录: {len(key_dirs)}")
            print(f"  • 存在的目录: {sum(1 for d in key_dirs if Path(d).exists())}")
            print(f"  • 检查的.keep文件: {keep_files_checked}")
            print(f"  • 找到的.keep文件: {keep_files_found}")
            
            if all_exist:
                print("所有关键目录都存在！")
                return True
            else:
                print("部分关键目录缺失！")
                return False
        else:
            print("初始化脚本执行失败！")
            return False
            
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        return False


if __name__ == "__main__":
    print("项目初始化脚本测试工具")
    print("=" * 50)
    
    success = test_init_script()
    
    if success:
        print("\n所有测试通过！项目初始化脚本工作正常。")
        sys.exit(0)
    else:
        print("\n测试发现问题，请检查初始化脚本。")
        sys.exit(1)
