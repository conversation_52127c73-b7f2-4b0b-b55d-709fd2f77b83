"""
医学图像检测与分割系统 - 工具模块

该模块提供项目所需的各种工具函数和类，包括：
- 路径管理
- 日志记录
- 数据处理
- 系统配置

"""

# 导入路径管理
from .paths import (
        # 主要架构目录
        YOLOSERVER_ROOT,

        # 配置目录
        CONFIGS_DIR,

        # 模型目录
        MODELS_DIR,
        CHECKPOINTS_DIR,
        PRETRAINED_DIR,
        ONNX_MODELS_DIR,
        TENSORRT_MODELS_DIR,

        # 数据目录
        DATA_DIR,
        RAW_DATA_DIR,
        TEMP_DATA_DIR,

        # 标注目录
        ORIGINAL_ANNOTATIONS_DIR,
        RAW_IMAGES_DIR,
        YOLO_ANNOTATIONS_DIR,
        COCO_ANNOTATIONS_DIR,

        # 训练数据集目录
        TRAIN_DIR,
        VAL_DIR,
        TEST_DIR,

        # 运行结果目录
        RUNS_DIR,

        # 日志目录
        LOGS_DIR,

        # 脚本目录
        SCRIPTS_DIR,

        # 文档目录
        DOCS_DIR,

        # 测试目录
        TESTS_DIR,
)

# 导入日志记录功能
from .logging_utils import (
    setup_logger,
    rename_log_file,
)

# 导入性能监控功能
from .performance_utils import (
    time_it,
)

# 导入配置管理功能
from .config_utils import (
    load_config,
    generate_default_config,
    merge_config,
)

# 导入数据集验证功能
from .dataset_validation import (
    verify_dataset_config,
    verify_split_uniqueness,
    delete_invalid_files,
)

from .configs import (
    DEFAULT_TRAIN_CONFIG,
    DEFAULT_VAL_CONFIG,
    DEFAULT_INFER_CONFIG,
)

from .model_utils import(
    copy_checkpoint_models,
)

__all__ = [
    # 路径管理
    'YOLOSERVER_ROOT',
    'LOGS_DIR',
    'MODELS_DIR',
    'DATA_DIR',

    # 日志记录
    'setup_logger',
    'rename_log_file',

    # 数据集验证
    'verify_dataset_config',
    'verify_split_uniqueness',
    'delete_invalid_files',

    # 配置管理
    'load_config',
    'generate_default_config',
    'merge_config',

    # 默认配置
    'DEFAULT_TRAIN_CONFIG',
    'DEFAULT_VAL_CONFIG',
    'DEFAULT_INFER_CONFIG',

    # 模型复制
    'copy_checkpoint_models',
]