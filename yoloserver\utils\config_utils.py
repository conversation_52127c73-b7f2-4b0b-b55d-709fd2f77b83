import logging
from pathlib import Path
import argparse
import yaml

# 支持两种导入方式：相对导入（从utils包内部）和绝对导入（从scripts等外部）
try:
    # 尝试相对导入（当作为utils包的一部分被导入时）
    from .paths import CONFIGS_DIR, RUNS_DIR
    from .configs import COMMENTED_TRAIN_CONFIG, DEFAULT_TRAIN_CONFIG
    from .configs import COMMENTED_VAL_CONFIG, DEFAULT_VAL_CONFIG
    from .configs import COMMENTED_INFER_CONFIG, DEFAULT_INFER_CONFIG
except ImportError:
    # 回退到绝对导入（当直接从scripts等目录运行时）
    from paths import CONFIGS_DIR, RUNS_DIR
    from configs import COMMENTED_TRAIN_CONFIG, DEFAULT_TRAIN_CONFIG
    from configs import COMMENTED_VAL_CONFIG, DEFAULT_VAL_CONFIG
    from configs import COMMENTED_INFER_CONFIG, DEFAULT_INFER_CONFIG

# 确保只包含官方参数
VALID_YOLO_TRAIN_ARGS = set(DEFAULT_TRAIN_CONFIG.keys())
VALID_YOLO_VAL_ARGS = set(DEFAULT_VAL_CONFIG.keys())
VALID_YOLO_INFER_ARGS = set(DEFAULT_INFER_CONFIG.keys())

# 布尔类型参数集合
BOOLEAN_PARAMS = {
    key for config in (DEFAULT_TRAIN_CONFIG, DEFAULT_VAL_CONFIG, DEFAULT_INFER_CONFIG)
    for key, value in config.items() if isinstance(value, bool)
}

logger = logging.getLogger(__name__)

def load_config(config_type='train'):
    """
    加载指定类型的配置文件
    :param config_type: 配置文件类型 (train, val, infer)
    :return: 配置字典或None
    """
    config_path = CONFIGS_DIR / f'{config_type}.yaml'

    if not config_path.exists():
        logger.warning(f"配置文件 {config_path} 不存在，将使用默认配置")
        return None

    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        logger.info(f"成功加载配置文件: {config_path}")
        return config
    except yaml.YAMLError as e:
        logger.error(f"解析YAML配置文件失败: {e}")
        return None
    except IOError as e:
        logger.error(f"读取配置文件失败: {e}")
        return None
    except Exception as e:
        logger.error(f"加载配置文件时发生未知错误: {e}")
        return None

def generate_default_config(config_type):
    """
    生成默认的配置文件
    :param config_type: 配置文件类型
    :return:
    """
    config_path = CONFIGS_DIR / f'{config_type}.yaml'
    
    if config_type == 'train':
        config_content = COMMENTED_TRAIN_CONFIG
    elif config_type == 'val':
        config_content = COMMENTED_VAL_CONFIG
    elif config_type == 'infer':
        config_content = COMMENTED_INFER_CONFIG
    else:
        logger.error(f"未知的配置文件类型: {config_type}")
        raise ValueError(f"配置文件类型错误: {config_type}, 目前仅支持train、val、infer这三种模式")
    
    try:
        with open(config_path, 'w', encoding='utf-8') as f:
            f.write(config_content)
        logger.info(f"生成默认 {config_type} 配置文件成功，文件路径为: {config_path}")
    except IOError as e:
        logger.error(f"写入默认 {config_type} 配置文件失败，请检查文件权限和路径是否正确，失败: {e}")
        raise
    except Exception as e:
        logger.error(f"生成配置文件 '{config_path.name}' 发生未知错误: {e}")
        raise

def _process_params_value(key, value):
    """
    处理参数值，进行类型转换和验证
    :param key: 参数名
    :param value: 参数值
    :return: 处理后的参数值
    """
    if value is None:
        return value

    # 布尔类型参数处理
    if key in BOOLEAN_PARAMS:
        if isinstance(value, bool):
            return value
        elif isinstance(value, str):
            return value.lower() in ('true', '1', 'yes', 'on')
        else:
            return bool(value)

    # 数值类型参数处理
    if isinstance(value, str):
        # 尝试转换为数值
        try:
            if '.' in value:
                return float(value)
            else:
                return int(value)
        except ValueError:
            # 如果不能转换为数值，保持字符串
            pass

    return value

def merge_config(args, yaml_config, mode='train'):
    """
    合并命令行参数、YAML配置文件参数和默认参数，按优先级CLI>YAML>默认值
    :param args: 通过argparse解析的参数
    :param yaml_config: 从YAML配置文件中加载的参数
    :param mode: 运行模式，支持train, val, infer
    :return:
    """
    # 1. 确定运行模式和相关配置
    if mode == 'train':
        valid_args = VALID_YOLO_TRAIN_ARGS
        default_config = DEFAULT_TRAIN_CONFIG
    elif mode == 'val':
        valid_args = VALID_YOLO_VAL_ARGS
        default_config = DEFAULT_VAL_CONFIG
    elif mode == 'infer':
        valid_args = VALID_YOLO_INFER_ARGS
        default_config = DEFAULT_INFER_CONFIG
    else:
        logger.error(f"{mode}模式不存在，仅支持train/val/infer三种模式")
        raise ValueError(f"{mode}模式不存在，仅支持train/val/infer三种模式")

    # 2. 初始化参数存储
    project_args = argparse.Namespace()
    yolo_args = argparse.Namespace()
    merged_params = default_config.copy()

    # 3. 合并YAML参数
    if hasattr(args, 'use_yaml') and args.use_yaml and yaml_config:
        for key, value in yaml_config.items():
            merged_params[key] = _process_params_value(key, value)
        logger.debug(f"合并YAML参数后: {merged_params}")

    # 4. 合并命令行参数
    cmd_args = {k: v for k, v in vars(args).items() if k != 'extra_args' and v is not None}
    for key, value in cmd_args.items():
        merged_params[key] = _process_params_value(key, value)
        setattr(project_args, f"{key}_specified", True)

    # 5. 处理动态参数
    if hasattr(args, 'extra_args'):
        if len(args.extra_args) % 2 != 0:
            logger.error("额外参数格式错误，参数列表必须成对出现，如 --key value")
            raise ValueError("额外参数格式错误")
        
        for i in range(0, len(args.extra_args), 2):
            key = args.extra_args[i].lstrip("-")
            value = args.extra_args[i+1]
            
            processed_value = _process_params_value(key, value)
            if processed_value == value:
                try:
                    if value.replace(".", "", 1).isdigit():
                        value = float(value) if '.' in value else int(value)
                    elif value.lower() in ("true", "false"):
                        value = value.lower() == "true"
                    elif value.lower() == "none":
                        value = None
                except ValueError:
                    logger.warning(f"无法转换额外参数{key}的值{value}")
                merged_params[key] = value
            else:
                merged_params[key] = processed_value
            
            # 标记额外的参数来源
            setattr(project_args, f"{key}_specified", True)

    # 6. 路径标准化
    # 数据集路径标准化
    if 'data' in merged_params and merged_params['data']:
        data_path = Path(merged_params['data'])
        if not data_path.is_absolute():
            data_path = CONFIGS_DIR / data_path
        merged_params['data'] = str(data_path.resolve())
        # 验证数据集配置文件是否存在
        if not data_path.exists():
            logger.warning(f"数据集配置文件'{data_path}'不存在")
        logger.info(f"标准化数据集路径: '{merged_params['data']}'")
    
    # 项目路径标准化
    if 'project' in merged_params and merged_params['project']:
        project_path = Path(merged_params['project'])
        if not project_path.is_absolute():
            project_path = RUNS_DIR / project_path
        merged_params['project'] = str(project_path)
        try:
            project_path.mkdir(parents=True, exist_ok=True)
        except PermissionError:
            logger.error(f"PermissionError: 无权限创建目录{project_path}")
            raise PermissionError(f"无权限创建目录{project_path}")
        logger.info(f"标准化project路径: '{merged_params['project']}'")

    # 7. 分离参数
    for key, value in merged_params.items():
        setattr(project_args, key, value)
        if key in valid_args:
            setattr(yolo_args, key, value)
        if yaml_config and key in yaml_config and not hasattr(project_args, f"{key}_specified"):
            setattr(project_args, f"{key}_specified", False)

    # 8. 返回分离后的参数
    return yolo_args, project_args