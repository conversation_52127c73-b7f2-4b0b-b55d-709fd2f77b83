# 主要实现COCO到YOLO格式的转换，并返回一个名称列表

import json
import logging
import datetime
import shutil
import sys
from pathlib import Path

from ultralytics.data.converter import convert_coco

# 添加父目录到路径，以便导入utils模块
current_file = Path(__file__).resolve()
utils_dir = current_file.parent.parent
yoloserver_dir = utils_dir.parent
if str(yoloserver_dir) not in sys.path:
    sys.path.insert(0, str(yoloserver_dir))

from utils.paths import RAW_DATA_DIR, YOLO_ANNOTATIONS_DIR, ORIGINAL_ANNOTATIONS_DIR, LOGS_DIR

logger = logging.getLogger(__name__)

def convert_coco_json_to_yolo(json_input_dir: Path, task: str = 'detection', cls91to80: bool = True):
    """
    将COCO格式的JSON文件转换为YOLO格式的txt文件到指定的目录。

    参数:
    - json_input_dir (Path): 包含COCO JSON文件的目录路径。
    - task (str): 任务类型，可以是'detection'或'segmentation'。

    返回值：
    - class_names (list): 类别名称列表。
    """
    logger.info(f"开始执行convert_coco转换,从{json_input_dir}转为YOLO模式")

    if not json_input_dir.exists():
        logger.error(f"COCO JSON输入目录不存在: {json_input_dir}")
        raise FileNotFoundError(f"COCO JSON输入目录不存在: {json_input_dir}")

    # 查找目录中所有的Json文件并提示数量
    json_file_found = list(json_input_dir.glob('*.json'))
    if not json_file_found:
        logger.error(f"未找到COCO JSON文件在目录: {json_input_dir}")
        raise FileNotFoundError(f"未找到COCO JSON文件在目录: {json_input_dir}")
    else:
        logger.info(f"找到 {len(json_file_found)} 个COCO JSON文件")

    # 检测json文件category字段是否一致并收集使用category_id
    first_categories_set = set()
    first_coco_json_path = json_file_found[0]
    all_used_category_ids = set()
    original_coco_id_to_name_map = {}

    for i, json_file_path in enumerate(json_file_found):
        try:
            with open(json_file_path, 'r', encoding='utf-8') as f:
                current_coco_data = json.load(f)
            current_category_set = set()
            for category in current_coco_data.get('categories', []):
                if 'id' in category and 'name' in category:
                    current_category_set.add((category['id'], category['name']))
            for annotation in current_coco_data.get('annotations', []):
                if 'category_id' in annotation:
                    all_used_category_ids.add(annotation['category_id'])

            if i == 0:
                first_categories_set = current_category_set
                for category in current_coco_data.get('categories', []):
                    if 'id' in category and 'name' in category:
                        original_coco_id_to_name_map[category['id']] = category['name']
                logger.info(f"已加载基准json文件，'{json_file_path.name}'的categories信息，并构建原始ID到名称映射")

            else:
                if first_categories_set != current_category_set:
                    logger.critical(f"第一个'{first_coco_json_path.name}'文件的categories与基准不一致: {json_file_path.name}")
                    raise ValueError(f"第一个'{first_coco_json_path.name}'文件的categories与基准不一致: {json_file_path.name}")
                logger.info(f"Json文件'{json_file_path.name}'的categories与基准一致")

        except json.JSONDecodeError as e:
            logger.error(f"处理Json文件'{json_file_path.name}'时出错: {e}")
            raise e
        except Exception as e:
            logger.error(f"读取或者处理Json文件'{json_file_path.name}'时出错: {e}")
            raise e

    # 提取实际使用的类别ID并构建最终的classes_name列表，用于data.yaml的name字段
    sorted_used_categories = sorted(list(all_used_category_ids))

    classes_name = []
    for category_id in sorted_used_categories:
        if category_id in original_coco_id_to_name_map:
            classes_name.append(original_coco_id_to_name_map[category_id])
        else:
            logger.warning(f"在annotation中找到category_id: {category_id} 对应的名称但在categories中没有找到对应的ID，故跳过此ID")
            classes_name.append(f"unknown_{category_id}")
    if not classes_name:
        logger.error("未能从所有json文件中的annotations中找到任何有效的类别名称，转换中止")
        raise ValueError("未能从所有json文件中的annotations中找到任何有效的类别名称，转换中止")
    else:
        logger.info(f"找到 {len(classes_name)} 个有效的类别名称: {classes_name}")

    if cls91to80:
        logger.info("将类别ID从COCO的91类映射到YOLO的80类")

    # 定义文件处理逻辑
    timestamp = datetime.datetime.now().strftime("%Y%m%d%H%M%S_%f")
    temp_dir_name = f"{timestamp}"
    temp_dir = RAW_DATA_DIR / temp_dir_name

    use_segment = (task == 'segmentation')

    # 调用ultralytics的convert_coco函数
    try:
        convert_coco(
            labels_dir=str(json_input_dir),
            save_dir=str(temp_dir),
            use_segments=use_segment,
            cls91to80=cls91to80,
            use_keypoints=False,
            lvis=False
        )
        logger.info(f"convert_coco转换完成到临时目录{temp_dir}")
    except Exception as e:
        logger.error(f"转换失败，请检查数据集格式是否正确，错误信息为: {e}")
        if temp_dir.exists():
            shutil.rmtree(temp_dir)
            logger.info(f"已删除临时目录{temp_dir}")
        return []

    # 移动coco数据到指定的临时存放点
    source_labels_in_temp = temp_dir / "labels"
    if not source_labels_in_temp.exists():
        logger.error(f"未找到临时目录中的labels目录: {source_labels_in_temp}")
        if temp_dir.exists():
            shutil.rmtree(temp_dir)
            logger.info(f"已删除临时目录{temp_dir}")
        return []

    # 确保YOLO_ANNOTATIONS_DIR存在
    YOLO_ANNOTATIONS_DIR.mkdir(parents=True, exist_ok=True)
    logger.info(f"开始将生成的TXT文件从{source_labels_in_temp} 移动到 {YOLO_ANNOTATIONS_DIR} 中")

    moved_count = 0
    for txt_file in source_labels_in_temp.glob("./*/*.txt"):
        try:
            shutil.move(str(txt_file), str(YOLO_ANNOTATIONS_DIR / txt_file.name))
            moved_count += 1
        except Exception as e:
            logger.error(f"移动文件{txt_file.name} 到 {YOLO_ANNOTATIONS_DIR}失败，错误信息为:{e}")

    logger.info(f"成功移动了{moved_count}个YOLO TXT 文件，到 {YOLO_ANNOTATIONS_DIR}")

    # 删除临时目录
    try:
        shutil.rmtree(temp_dir)
        logger.info(f"成功删除临时目录 {temp_dir}")
    except Exception as e:
        logger.error(f"删除临时目录{source_labels_in_temp}失败，错误信息为:{e}")

    logger.info(f"COCO JSON 到 YOLO TXT 转换流程完成".center(60, "="))

    return classes_name

if __name__ == "__main__":
    classes_name = convert_coco_json_to_yolo(
        json_input_dir=Path(r"E:\GitCloneProject\BrainTumorDetection\yoloserver\data\raw\original_annotations"),
        task ="segmentation",
        )
    print(classes_name)
