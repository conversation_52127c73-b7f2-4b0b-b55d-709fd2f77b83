"""
医学图像检测与分割系统 - 数据集验证模块

该模块提供YOLO数据集的结构、内容和划分正确性验证功能。
在模型训练之前确保数据集的高质量，避免训练过程中的错误。

主要功能：
- 验证data.yaml配置文件和数据集内容
- 检查数据集分割的唯一性
- 删除不合法的数据文件

"""

import logging
import yaml
import random
from pathlib import Path
from typing import Tuple, List, Dict, Set
import os

logger = logging.getLogger(__name__)


def verify_dataset_config(
    yaml_path: Path, 
    current_logger: logging.Logger, 
    mode: str, 
    task_type: str
) -> Tuple[bool, List[Dict]]:
    """
    验证YOLO数据集配置文件和数据集内容的正确性
    
    Args:
        yaml_path (Path): data.yaml文件的路径
        current_logger (logging.Logger): 用于记录日志的logger实例
        mode (str): 验证模式，"FULL" (完整验证) 或 "SAMPLE" (抽样验证)
        task_type (str): 任务类型，"detection" 或 "segmentation"
        
    Returns:
        Tuple[bool, List[Dict]]: (验证是否通过, 不合法样本信息列表)
    """
    current_logger.info(f"开始验证数据集配置 - 模式: {mode}, 任务类型: {task_type}")
    current_logger.info(f"配置文件路径: {yaml_path}")
    
    invalid_data_list = []
    
    try:
        # 1. 读取并解析data.yaml文件
        if not yaml_path.exists():
            current_logger.error(f"配置文件不存在: {yaml_path}")
            return False, []
            
        with open(yaml_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
            
        current_logger.info("成功读取data.yaml配置文件")
        
        # 2. 验证配置文件基本结构
        required_keys = ['path', 'train', 'val', 'nc', 'names']
        for key in required_keys:
            if key not in config:
                current_logger.error(f"配置文件缺少必需字段: {key}")
                return False, []
                
        # 3. 验证类别数量与类别名称列表的一致性
        nc = config['nc']
        names = config['names']
        
        if not isinstance(nc, int) or nc <= 0:
            current_logger.error(f"类别数量nc必须是正整数，当前值: {nc}")
            return False, []
            
        if not isinstance(names, list):
            current_logger.error(f"类别名称names必须是列表，当前类型: {type(names)}")
            return False, []
            
        if len(names) != nc:
            current_logger.error(f"类别数量nc({nc})与类别名称列表长度({len(names)})不一致")
            return False, []
            
        current_logger.info(f"配置验证通过 - 类别数量: {nc}, 类别名称: {names}")
        
        # 4. 验证数据集分割
        splits = ['train', 'val']
        if 'test' in config:
            splits.append('test')
            
        total_images = 0
        total_valid_images = 0
        
        for split in splits:
            if split not in config:
                continue
                
            split_path = Path(config[split])
            current_logger.info(f"验证{split}数据集: {split_path}")
            
            if not split_path.exists():
                current_logger.error(f"{split}数据集目录不存在: {split_path}")
                return False, []
                
            # 获取图像文件列表
            image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
            image_files = []

            for ext in image_extensions:
                # 避免重复统计，使用集合去重
                files_lower = set(split_path.glob(f'*{ext}'))
                files_upper = set(split_path.glob(f'*{ext.upper()}'))
                image_files.extend(list(files_lower | files_upper))
                
            if not image_files:
                current_logger.warning(f"{split}数据集目录为空: {split_path}")
                continue
                
            current_logger.info(f"{split}数据集包含{len(image_files)}张图像")
            total_images += len(image_files)
            
            # 根据模式选择要验证的图像
            if mode == "SAMPLE" and len(image_files) > 100:
                sample_size = min(100, len(image_files))
                image_files = random.sample(image_files, sample_size)
                current_logger.info(f"抽样验证模式，随机选择{len(image_files)}张图像进行验证")
                
            # 验证每张图像及其标签
            split_valid_count = 0
            for image_path in image_files:
                is_valid, error_msg = _validate_image_and_label(
                    image_path, nc, task_type, current_logger
                )
                
                if is_valid:
                    split_valid_count += 1
                else:
                    # 构建标签文件路径
                    label_dir = split_path.parent / 'labels'
                    label_path = label_dir / f"{image_path.stem}.txt"
                    
                    invalid_data_list.append({
                        'image_path': str(image_path),
                        'label_path': str(label_path),
                        'error_message': error_msg
                    })
                    
            total_valid_images += split_valid_count
            current_logger.info(f"{split}数据集验证完成 - 有效图像: {split_valid_count}/{len(image_files)}")
            
        # 5. 输出验证结果
        if invalid_data_list:
            current_logger.warning(f"发现{len(invalid_data_list)}个不合法样本")
            for invalid_item in invalid_data_list[:5]:  # 只显示前5个错误
                current_logger.warning(f"不合法样本: {invalid_item['image_path']} - {invalid_item['error_message']}")
            if len(invalid_data_list) > 5:
                current_logger.warning(f"... 还有{len(invalid_data_list) - 5}个不合法样本")
                
        validation_passed = len(invalid_data_list) == 0
        current_logger.info(f"数据集配置验证{'通过' if validation_passed else '失败'}")
        current_logger.info(f"总计图像: {total_images}, 有效图像: {total_valid_images}")
        
        return validation_passed, invalid_data_list
        
    except Exception as e:
        current_logger.error(f"验证过程中发生错误: {str(e)}")
        return False, []


def _validate_image_and_label(
    image_path: Path, 
    nc: int, 
    task_type: str, 
    current_logger: logging.Logger
) -> Tuple[bool, str]:
    """
    验证单张图像及其对应的标签文件
    
    Args:
        image_path (Path): 图像文件路径
        nc (int): 类别数量
        task_type (str): 任务类型
        current_logger (logging.Logger): 日志记录器
        
    Returns:
        Tuple[bool, str]: (是否有效, 错误信息)
    """
    try:
        # 构建标签文件路径
        label_dir = image_path.parent.parent / 'labels'
        label_path = label_dir / f"{image_path.stem}.txt"
        
        # 检查标签文件是否存在
        if not label_path.exists():
            return False, f"标签文件不存在: {label_path}"
            
        # 读取并验证标签文件内容
        with open(label_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            
        # 空标签文件是允许的（表示无目标）
        if not lines:
            return True, ""
            
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            if not line:
                continue
                
            parts = line.split()
            
            # 验证格式
            if task_type == "detection":
                if len(parts) != 5:
                    return False, f"检测任务标签格式错误，第{line_num}行应包含5个值，实际{len(parts)}个"
            elif task_type == "segmentation":
                if len(parts) < 7 or (len(parts) - 1) % 2 != 0:
                    return False, f"分割任务标签格式错误，第{line_num}行坐标对数量不正确"
            else:
                return False, f"不支持的任务类型: {task_type}"
                
            # 验证数值有效性
            try:
                values = [float(part) for part in parts]
            except ValueError:
                return False, f"第{line_num}行包含非数值数据"
                
            # 验证类别ID
            class_id = int(values[0])
            if class_id < 0 or class_id >= nc:
                return False, f"第{line_num}行类别ID({class_id})超出范围[0, {nc-1}]"
                
            # 验证坐标值范围
            coords = values[1:]
            for coord in coords:
                if coord < 0 or coord > 1:
                    return False, f"第{line_num}行坐标值({coord})超出范围[0, 1]"
                    
        return True, ""
        
    except Exception as e:
        return False, f"验证标签文件时发生错误: {str(e)}"


def verify_split_uniqueness(yaml_path: Path, current_logger: logging.Logger) -> bool:
    """
    检查train、val、test三个数据集分割之间是否存在重复的图像文件
    
    Args:
        yaml_path (Path): data.yaml文件的路径
        current_logger (logging.Logger): 用于记录日志的logger实例
        
    Returns:
        bool: 表示分割唯一性验证是否通过（True为无重复，False为存在重复）
    """
    current_logger.info("开始验证数据集分割唯一性")
    
    try:
        # 读取配置文件
        with open(yaml_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
            
        splits = ['train', 'val']
        if 'test' in config:
            splits.append('test')
            
        # 收集每个分割的图像文件名
        split_files = {}
        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
        
        for split in splits:
            if split not in config:
                continue
                
            split_path = Path(config[split])
            if not split_path.exists():
                current_logger.warning(f"{split}数据集目录不存在: {split_path}")
                continue
                
            files = set()
            for ext in image_extensions:
                files.update(f.name for f in split_path.glob(f'*{ext}'))
                files.update(f.name for f in split_path.glob(f'*{ext.upper()}'))
                
            split_files[split] = files
            current_logger.info(f"{split}数据集包含{len(files)}个图像文件")
            
        # 检查重复
        duplicates_found = False
        for i, split1 in enumerate(splits):
            if split1 not in split_files:
                continue
            for split2 in splits[i+1:]:
                if split2 not in split_files:
                    continue
                    
                common_files = split_files[split1] & split_files[split2]
                if common_files:
                    duplicates_found = True
                    current_logger.error(f"{split1}和{split2}数据集之间存在{len(common_files)}个重复文件")
                    for file in list(common_files)[:5]:  # 只显示前5个重复文件
                        current_logger.error(f"重复文件: {file}")
                    if len(common_files) > 5:
                        current_logger.error(f"... 还有{len(common_files) - 5}个重复文件")
                        
        if not duplicates_found:
            current_logger.info("数据集分割唯一性验证通过，无重复文件")
            
        return not duplicates_found
        
    except Exception as e:
        current_logger.error(f"验证分割唯一性时发生错误: {str(e)}")
        return False


def delete_invalid_files(invalid_data_list: list, current_logger: logging.Logger):
    """
    根据传入的不合法文件列表，删除对应的图像文件和标签文件
    
    Args:
        invalid_data_list (list): verify_dataset_config返回的不合法样本列表
        current_logger (logging.Logger): 用于记录日志的logger实例
    """
    current_logger.warning("开始删除不合法文件 - 此操作不可逆！")
    
    if not invalid_data_list:
        current_logger.info("没有需要删除的不合法文件")
        return
        
    deleted_images = 0
    deleted_labels = 0
    
    for invalid_item in invalid_data_list:
        image_path = Path(invalid_item['image_path'])
        label_path = Path(invalid_item['label_path'])
        
        try:
            # 删除图像文件
            if image_path.exists():
                image_path.unlink()
                deleted_images += 1
                current_logger.info(f"已删除图像文件: {image_path}")
            else:
                current_logger.warning(f"图像文件不存在，跳过: {image_path}")
                
            # 删除标签文件
            if label_path.exists():
                label_path.unlink()
                deleted_labels += 1
                current_logger.info(f"已删除标签文件: {label_path}")
            else:
                current_logger.warning(f"标签文件不存在，跳过: {label_path}")
                
        except Exception as e:
            current_logger.error(f"删除文件时发生错误: {image_path} - {str(e)}")
            
    current_logger.info(f"删除操作完成 - 图像文件: {deleted_images}, 标签文件: {deleted_labels}")
