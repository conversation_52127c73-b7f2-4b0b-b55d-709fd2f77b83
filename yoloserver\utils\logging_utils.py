import logging
from datetime import datetime
from pathlib import Path
import re

def setup_logger(
    base_path: Path,
    log_type: str = "general",
    model_name: str = None,
    encoding: str = "utf-8",
    log_level: int = logging.INFO,
    temp_log: bool = False,
    logger_name: str = "<PERSON><PERSON><PERSON> Default"
):
    """
    配置日志记录器，将日志保存到指定路径的子目录当中，并同时输出到控制台
    日志文件名为类型 + 时间戳
    
    :param base_path: 日志文件的根路径
    :param log_type: 日志的类型
    :param model_name: 使用的模型名称
    :param encoding: 文件编码
    :param log_level: 日志等级
    :param temp_log: 是否使用临时文件名
    :param logger_name: 日志记录器的名称
    """
    # 1. 构建日志文件完整的存放路径
    log_dir = base_path / log_type
    log_dir.mkdir(parents=True, exist_ok=True)
    
    # 2. 生成一个带时间戳的日志文件名
    timestamp = datetime.now().strftime("%Y%m%d-%H%M%S")
    
    # 根据temp_log参数，生成不同的日志文件名
    prefix = "temp_" if temp_log else log_type.replace(" ", "-")
    log_filename_parts = [prefix, timestamp]
    
    if model_name:
        log_filename_parts.append(model_name.replace(" ", "-"))
    
    log_filename = "_".join(log_filename_parts) + ".log"
    log_file = log_dir / log_filename
    
    # 3. 获取或创建指定名称的logger实例
    logger = logging.getLogger(logger_name)
    # 设定日志记录器记录最低记录级别
    logger.setLevel(log_level)
    # 阻止日志事件传播到父级logger
    logger.propagate = False
    
    # 4. 避免重复添加日志处理器
    # 删除现有所有handlers防止重复记录
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # 5. 创建文件处理器，将日志写入到文件当中
    file_handler = logging.FileHandler(log_file, encoding=encoding)
    file_handler.setFormatter(logging.Formatter("%(asctime)s - %(levelname)s - %(name)s: %(message)s"))
    logger.addHandler(file_handler)
    
    # 6. 创建控制台处理器，将日志输出到控制台
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(logging.Formatter("%(asctime)s - %(levelname)s - %(name)s: %(message)s"))
    logger.addHandler(console_handler)
    
    # 输出初始化信息到日志
    logger.info(f"日志记录器已启动，日志文件保存在: {log_file}")
    logger.info(f"日志记录器的根目录: {base_path}")
    logger.info(f"日志记录器的名称: {logger_name}")
    logger.info(f"日志记录器的类型: {log_type}")
    logger.info(f"日志记录器的级别: {logging.getLevelName(log_level)}")
    logger.info("=" * 60)
    logger.info("日志记录器初始化成功".center(60))
    logger.info("=" * 60)
    
    return logger


def rename_log_file(logger_obj, save_dir, model_name, encoding="utf-8"):
    """
    重命名日志文件，将临时日志文件重命名为更具描述性的名称

    该函数的核心思想是：找到现有的文件日志处理器 -> 关闭它 -> 从日志器中移除它 ->
    重命名硬盘上的文件 -> 创建一个新的文件日志处理器指向新的文件名 -> 将新处理器重新添加到日志器中

    :param logger_obj: 日志记录器对象
    :param save_dir: 保存目录路径，用于提取目录名作为前缀
    :param model_name: 模型名称
    :param encoding: 文件编码，默认 utf-8
    """
    if not logger_obj or not hasattr(logger_obj, 'handlers'):
        logger_obj.warning("无效的日志记录器对象")
        return

    # 1. 遍历日志器处理器列表，寻找文件处理器
    for handler in list(logger_obj.handlers):
        # 2. 识别文件处理器
        if isinstance(handler, logging.FileHandler):
            # 3. 获取旧日志文件路径
            old_log_file = Path(handler.baseFilename)

            # 4. 解析时间戳和构建新文件名
            try:
                # 解析旧文件名中的时间戳
                # 支持格式：temp_YYYYMMDD-HHMMSS_modelname.log 或 log_type_YYYYMMDD-HHMMSS_modelname.log
                filename_stem = old_log_file.stem

                # 使用正则表达式提取时间戳
                timestamp_pattern = r'(\d{8}-\d{6})'
                timestamp_match = re.search(timestamp_pattern, filename_stem)

                if timestamp_match:
                    timestamp = timestamp_match.group(1)
                else:
                    # 如果无法解析时间戳，使用当前时间
                    timestamp = datetime.now().strftime("%Y%m%d-%H%M%S")
                    logger_obj.warning(f"无法从文件名中解析时间戳，使用当前时间: {timestamp}")

                # 获取目录前缀（如 train1, val2 等）
                train_prefix = Path(save_dir).name if save_dir else "unknown"

                # 构建新文件名：目录前缀_时间戳_模型名.log
                new_filename_parts = [train_prefix, timestamp]
                if model_name:
                    new_filename_parts.append(model_name.replace(" ", "-"))

                new_filename = "_".join(new_filename_parts) + ".log"
                new_log_file = old_log_file.parent / new_filename

            except Exception as e:
                logger_obj.error(f"构建新文件名时发生错误: {e}")
                return

            # 5. 关闭旧的文件处理器
            try:
                handler.close()
            except Exception as e:
                logger_obj.warning(f"关闭旧文件处理器时发生警告: {e}")

            # 6. 从日志器中移除旧的处理器
            logger_obj.removeHandler(handler)

            # 7. 执行文件重命名
            if old_log_file.exists():
                try:
                    # 检查新文件是否已存在，如果存在则添加序号
                    counter = 1
                    original_new_log_file = new_log_file
                    while new_log_file.exists():
                        name_parts = original_new_log_file.stem.split('_')
                        name_parts.append(f"v{counter}")
                        new_filename = "_".join(name_parts) + ".log"
                        new_log_file = original_new_log_file.parent / new_filename
                        counter += 1

                    # 执行重命名
                    old_log_file.rename(new_log_file)
                    logger_obj.info(f"日志文件已成功重命名: {old_log_file.name} -> {new_log_file.name}")

                except OSError as e:
                    logger_obj.error(f"重命名日志文件失败: {e}")
                    # 重命名失败时，重新添加指向旧路径的处理器，确保日志连续性
                    try:
                        fallback_handler = logging.FileHandler(old_log_file, encoding=encoding)
                        fallback_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(name)s: %(message)s'))
                        logger_obj.addHandler(fallback_handler)
                        logger_obj.warning(f"重命名失败，继续使用原文件: {old_log_file}")
                    except Exception as fallback_error:
                        logger_obj.error(f"创建备用文件处理器失败: {fallback_error}")
                    return
                except Exception as e:
                    logger_obj.error(f"重命名过程中发生未知错误: {e}")
                    return
            else:
                logger_obj.warning(f"旧日志文件不存在: {old_log_file}")
                # 如果旧文件不存在，直接创建新文件处理器

            # 8. 添加新的文件处理器
            try:
                new_handler = logging.FileHandler(new_log_file, encoding=encoding)
                new_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(name)s: %(message)s'))
                logger_obj.addHandler(new_handler)
                logger_obj.info(f"新日志处理器已创建，指向文件: {new_log_file}")
            except Exception as e:
                logger_obj.error(f"创建新文件处理器失败: {e}")
                return

            # 9. 跳出循环（假设只有一个主文件处理器）
            break
    else:
        logger_obj.warning("未找到文件处理器，无法执行重命名操作")

# 记录命令行参数和YAML参数信息
def log_parameters(args, exclude_params, logger):
    """
    记录命令行参数和YAML参数信息
    :param args:
    :param exclude parms:
    :param logger:
    :return:
    """
    params_dict={}
    for key, value in vars(args).items():
        if key not in exclude_params and not key.endswith(" specified"):
            source ='命令行'if getattr(args, f"{key}_specified",False)else 'YAML'
            logger.info(f"{key:<20}:{value}[来源 {source}]")
            params_dict[key]= value
    return params_dict

# 测试代码
if __name__ == "__main__":
    from paths import LOGS_DIR  # 需要定义LOGS_DIR路径
    logger = setup_logger(
        base_path=LOGS_DIR,
        log_type="test_log",
        model_name=None
    )
    logger.info("测试日志记录器")