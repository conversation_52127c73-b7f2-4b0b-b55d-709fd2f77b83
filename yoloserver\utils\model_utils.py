from datetime import datetime
from pathlib import Path
import shutil
import os
import logging

def copy_checkpoint_models(train_dir, model_filename, checkpoint_dir, logger):
    """
    拷贝预训练模型到结果目录，确保实验完整性，便于版本管理和复现。
    
    :param train_dir: 训练结果目录路径
    :param model_filename: 模型文件名
    :param checkpoint_dir: 检查点目录路径
    :param logger: 日志记录器对象
    """

    if not isinstance(train_dir, Path):
        logger.error(f"训练结果目录路径类型错误，应为Path类型，但为{type(train_dir)}")
        return
    if not isinstance(checkpoint_dir, Path) or not checkpoint_dir.is_dir():
        logger.error(f"检查点目录路径类型错误或目录不存在，应为Path类型且为目录，但为{type(checkpoint_dir)}")
        return
    
    # 准备准备好的新的模型文件名
    data_str = datetime.now().strftime("%Y%m%d-%H%M%S")
    base_model_name = Path(model_filename).stem
    train_suffix = train_dir.name

    # 遍历进行复制
    for model_type in ["best", "last"]:
        src_path = train_dir / "weights" / f"{model_type}.pt"
        if src_path.exists():
            checkpoint_name = f"{train_suffix}_{data_str}_{base_model_name}_{model_type}.pt"
            dst_path = checkpoint_dir / checkpoint_name
            try:
                shutil.copy(src_path, dst_path)
                logger.info(f"模型拷贝: {src_path} -> {dst_path}")
            except FileNotFoundError:
                logger.warning(f"源文件不存在: {src_path}")
            except shutil.SameFileError:
                logger.error(f"源文件与目标文件相同: {src_path},无法复制")
            except PermissionError:
                logger.error(f"没有权限复制文件: {src_path}")
        else:
            logger.warning(f"源文件不存在: {model_type}.pt")

        
