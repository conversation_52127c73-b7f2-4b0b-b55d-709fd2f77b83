"""
医学图像检测与分割系统 - 路径配置模块

该模块定义了项目中所有目录路径的标准化配置，遵循企业级开发规范。
支持医学影像数据的完整工作流程：数据预处理、模型训练、推理部署。

"""

from pathlib import Path
import os
import logging
from typing import List, Dict, Any, Optional

# 配置日志
logger = logging.getLogger(__name__)

# ================================
# 项目根目录配置
# ================================
YOLOSERVER_ROOT = Path(__file__).resolve().parents[1]  # YOLO服务项目根目录

# ================================
# 配置文件目录
# ================================
CONFIGS_DIR = YOLOSERVER_ROOT / "configs"              # 全局配置目录

# ================================
# 模型相关目录
# ================================
MODELS_DIR = YOLOSERVER_ROOT / "models"                # 模型根目录
CHECKPOINTS_DIR = MODELS_DIR / "checkpoints"        # 训练检查点
PRETRAINED_DIR = MODELS_DIR / "pretrained"          # 预训练模型
ONNX_MODELS_DIR = MODELS_DIR / "onnx"               # ONNX优化模型
TENSORRT_MODELS_DIR = MODELS_DIR / "tensorrt"       # TensorRT优化模型

# ================================
# 数据管理目录
# ================================
DATA_DIR = YOLOSERVER_ROOT / "data"                    # 数据根目录
RAW_DATA_DIR = DATA_DIR / "raw"                     # 原始数据
TEMP_DATA_DIR = DATA_DIR / "temp"                   # 临时数据

# ================================
# 原始医学影像数据目录
# ================================
RAW_IMAGES_DIR = RAW_DATA_DIR / "images"            # 原始医学影像
ORIGINAL_ANNOTATIONS_DIR = RAW_DATA_DIR / "original_annotations"    # 原始非yolo格式标注文件存放目录

# ================================
# 标注数据目录
# ================================
YOLO_ANNOTATIONS_DIR = RAW_DATA_DIR / "yolo"     # YOLO格式标注
COCO_ANNOTATIONS_DIR = RAW_DATA_DIR / "coco"     # COCO格式标注

# ================================
# 训练数据集目录
# ================================
TRAIN_DIR = DATA_DIR / "train"                  # 训练集
VAL_DIR = DATA_DIR / "val"                      # 验证集
TEST_DIR = DATA_DIR / "test"                    # 测试集

# ================================
# 运行结果目录
# ================================
RUNS_DIR = YOLOSERVER_ROOT / "runs"                    # 运行结果根目录

# ================================
# 日志和监控目录
# ================================
LOGS_DIR = YOLOSERVER_ROOT / "logs"                    # 日志根目录

# ================================
# 脚本和工具目录
# ================================
SCRIPTS_DIR = YOLOSERVER_ROOT / "scripts"              # 脚本根目录

# ================================
# 文档目录
# ================================
DOCS_DIR = YOLOSERVER_ROOT / "docs"                    # 文档根目录

# ================================
# 测试目录
# ================================
TESTS_DIR = YOLOSERVER_ROOT / "tests"                  # 测试根目录

# ================================
# 路径管理功能
# ================================

for _path in [
        # 主要架构目录
        YOLOSERVER_ROOT,

        # 配置目录
        CONFIGS_DIR,

        # 模型目录
        MODELS_DIR,
        CHECKPOINTS_DIR,
        PRETRAINED_DIR,
        ONNX_MODELS_DIR,
        TENSORRT_MODELS_DIR,

        # 数据目录
        DATA_DIR,
        RAW_DATA_DIR,
        TEMP_DATA_DIR,

        # 标注目录
        ORIGINAL_ANNOTATIONS_DIR,
        RAW_IMAGES_DIR,
        YOLO_ANNOTATIONS_DIR,
        COCO_ANNOTATIONS_DIR,

        # 训练数据集目录
        TRAIN_DIR,
        VAL_DIR,
        TEST_DIR,

        # 运行结果目录
        RUNS_DIR,

        # 日志目录
        LOGS_DIR,

        # 脚本目录
        SCRIPTS_DIR,

        # 文档目录
        DOCS_DIR,

        # 测试目录
        TESTS_DIR,
    ]:
    _path.mkdir(parents=True, exist_ok=True)
