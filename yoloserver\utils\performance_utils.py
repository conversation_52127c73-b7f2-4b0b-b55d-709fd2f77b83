import logging
import time
from functools import wraps

# 创建默认日志记录器
_default_logger = logging.getLogger(__name__)

def format_time_auto_unit(total_seconds: float) -> str:
    """将秒转换为合适的时间单位并格式化输出"""
    if total_seconds < 1e-6:  # 小于1微秒
        return f"{total_seconds * 1e9:.3f} ns"
    elif total_seconds < 1e-3:  # 小于1毫秒
        return f"{total_seconds * 1e6:.3f} μs"
    elif total_seconds < 1:  # 小于1秒
        return f"{total_seconds * 1000:.3f} ms"
    else:
        return f"{total_seconds:.3f} s"

def time_it(iterations: int = 1, name: str = None, logger_instance=None):
    """
    用于记录函数执行耗时的装饰器函数
    :param iterations: 函数执行次数，如果大于1，记录平均耗时；等于1，单次执行耗时
    :param name: 用于日志输出的函数类别名称
    :param logger_instance: 日志记录器实例
    """
    logger_to_use = logger_instance if logger_instance is not None else _default_logger

    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            func_display_name = name if name is not None else func.__name__
            total_elapsed_time = 0.0
            result = None
            
            for i in range(iterations):
                start_time = time.perf_counter()
                result = func(*args, **kwargs)
                end_time = time.perf_counter()
                total_elapsed_time += (end_time - start_time)
                
            avg_elapsed_time = total_elapsed_time / iterations
            formatted_avg_time = format_time_auto_unit(avg_elapsed_time)
            
            if iterations == 1:
                logger_to_use.info(f"性能测试:函数 {func_display_name} 执行耗时: {formatted_avg_time}")
            else:
                logger_to_use.info(
                    f"性能测试:函数 {func_display_name} 执行耗时: {iterations} 次，平均耗时: {formatted_avg_time}"
                )
            
            return result
        return wrapper
    return decorator


if __name__ == "__main__":
    # 测试代码 - 创建简单的日志配置
    logger = logging.getLogger("performance_test")
    logger.setLevel(logging.INFO)
    handler = logging.StreamHandler()
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    
    # 使用装饰器测试函数
    @time_it(iterations=5, name="测试函数", logger_instance=logger)
    def test_function():
        time.sleep(0.5)
        print("测试函数执行完成")
    
    test_function()